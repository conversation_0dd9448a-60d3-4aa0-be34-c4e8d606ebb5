import { OrgMember } from '../../../modules/members/entities/org-member.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity('signed_prekeys')
export class SignedPreKeyEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  memberId: string;

  @Column()
  keyId: number;

  @Column('text')
  publicKey: string;

  @Column('text')
  signature: string;

  @Column({ default: false })
  isUsed: boolean;

  @Column({ type: 'timestamp' })
  expireAt: Date;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'memberId' })
  member: OrgMember;
}
