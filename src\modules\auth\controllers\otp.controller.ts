import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Get,
  Req,
  Res,
  UseInterceptors,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { OtpService } from '../services/otp.service';
import { AuthService } from '../services/auth.service';
import { Public } from '../../../common/decorators/public.decorator';
import { RequestOtpDto } from '../dto/request-otp.dto';
import { VerifyOtpDto } from '../dto/verify-otp.dto';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';
import { UsePermanentUrls } from '../../../common/decorators/image.decorator';
import { RateLimit } from 'src/common/decorators/rate-limit.decorator';

@ApiTags('OTP')
@Controller('auth/otp')
export class OtpController {
  constructor(
    private readonly otpService: OtpService,
    private readonly authService: AuthService,
  ) {}

  @Post('request')
  @RateLimit({ limit: 3, ttl: 300, name: 'otp-request' })
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Request OTP for login or verification' })
  @ApiResponse({ status: 200, description: 'OTP sent successfully' })
  async requestOtp(
    @Body() requestOtpDto: RequestOtpDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const result = await this.otpService.requestOtp(requestOtpDto);
    response.locals.message = result;
    return '';
  }

  @Post('verify')
  @Public()
  @RateLimit({ limit: 5, ttl: 300, name: 'otp-verify' })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Verify OTP sent to user' })
  @ApiResponse({ status: 200, description: 'OTP verified successfully' })
  async verifyOtp(
    @Body() verifyOtpDto: VerifyOtpDto,
    @Res({ passthrough: true }) response: Response,
    @Req() req: Request,
  ) {
    const result = await this.otpService.verifyOtp(verifyOtpDto, req);
    response.locals.message = 'OTP Verified Successfully';
    return result;
  }

  @Post('resend')
  @Public()
  @RateLimit({ limit: 2, ttl: 600, name: 'otp-resend' })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Resend OTP to user' })
  @ApiResponse({ status: 200, description: 'OTP resent successfully' })
  async resendOtp(
    @Body() requestOtpDto: RequestOtpDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const result = await this.otpService.resendOtp(requestOtpDto);
    response.locals.message = 'OTP Resent Successfully';
    return result;
  }

  @Get('profile')
  @RateLimit({ limit: 30, ttl: 60, name: 'profile-fetch' })
  @UseInterceptors(ImageUrlInterceptor)
  @UsePermanentUrls(['imageUrl'])
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get authenticated user profile' })
  @ApiResponse({
    status: 200,
    description: 'User details fetched successfully',
  })
  getProfile(@Req() req) {
    (req as any).res.locals.message = 'User details fetched successfully';
    return this.authService.getUserDetails(req.user);
  }
}
