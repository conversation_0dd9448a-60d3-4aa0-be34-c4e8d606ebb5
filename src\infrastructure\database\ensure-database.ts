import { Client } from 'pg';

export async function ensureDatabase() {
  const client = new Client({
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    user: process.env.DB_USERNAME || 'postgres',
    password: process.env.DB_PASSWORD || 'root',
    database: 'postgres', // connect to default system db first
  });

  await client.connect();

  const dbName = process.env.DB_DATABASE || 'chat_app';
  const result = await client.query(
    `SELECT 1 FROM pg_database WHERE datname = $1`,
    [dbName],
  );

  if (result.rowCount === 0) {
    await client.query(`CREATE DATABASE "${dbName}"`);
    console.log(`✅ Database ${dbName} created`);
  }

  await client.end();
}
