import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import * as cookieParser from 'cookie-parser';
import * as cors from 'cors';
import { ConfigService } from '@nestjs/config';
import './config/env.config';
import helmet from 'helmet';
import {
  LoggingInterceptor,
  TransformInterceptor,
} from './common/interceptors';
import { AllExceptionsFilter } from './common/filters/all-exceptions.filter';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { sdk } from './tracing';
import { Logger, ValidationPipe } from '@nestjs/common';
import { ensureDatabase } from './infrastructure/database/ensure-database';

async function bootstrap() {
  // Initialize tracing
  sdk.start();
  Logger.log('OpenTelemetry tracing initialized');

  // Ensure database is ready
  await ensureDatabase();

  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Global Pipes for validation
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );
  app.use(helmet());
  app.use(cookieParser());
  const allowedOrigins = process.env.ALLOWED_ORIGINS
    ? process.env.ALLOWED_ORIGINS.split(',')
    : [
        'https://talkio.invictainnovations.com',
        'http://************:3001',
        'http://localhost:3001',
      ];

  app.use(
    cors({
      origin: (origin, callback) => {
        if (!origin) {
          return callback(null, true);
        }
        if (allowedOrigins.includes(origin)) {
          return callback(null, true);
        }
        return callback(new Error(`CORS blocked for origin: ${origin}`));
      },

      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'Accept',
        'X-Signature',
        'X-Device-Name',
        'X-Timestamp',
        'X-Public-Key',
        'X-Challenge',
      ],
      exposedHeaders: [
        'Authorization',
        'X-Signature',
        'X-Device-Name',
        'X-Timestamp',
        'X-Public-Key',
        'X-Challenge',
      ],
    }),
  );

  // Global interceptors and filters
  app.useGlobalInterceptors(
    new LoggingInterceptor(),
    new TransformInterceptor(),
  );
  app.useGlobalFilters(new AllExceptionsFilter());

  // Global API prefix
  const apiPrefix = configService.get<string>('API_PREFIX', 'api/v1');
  app.setGlobalPrefix(apiPrefix);

  // Swagger setup
  const swaggerConfig = new DocumentBuilder()
    .setTitle('API Documentation')
    .setDescription('API endpoints and schema')
    .setVersion('1.0')
    .addBearerAuth()
    .build();

  const document = SwaggerModule.createDocument(app, swaggerConfig);
  SwaggerModule.setup(`${apiPrefix}/docs`, app, document);

  const port = configService.get<number>('PORT') || 3000;
  await app.listen(port);
  Logger.log(`Application running on: http://localhost:${port}/${apiPrefix}`);
  Logger.log(
    `Swagger docs available at: http://localhost:${port}/${apiPrefix}/docs`,
  );
}

bootstrap();
