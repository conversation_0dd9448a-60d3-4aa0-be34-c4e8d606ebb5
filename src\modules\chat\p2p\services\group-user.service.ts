import {
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';
import { GroupsService } from 'src/modules/groups/services/groups.service';
import { GroupMembersService } from 'src/modules/groups/services/group-member.service';
import { GroupMessagesService } from '../../../messages/services/group-messages.service';
import { GroupEncryptionService } from 'src/modules/groups/services/group-encryption.service';
import { MessageThreadService } from '../../../messages/services/message-threads.service';
import { GroupMessage } from '../../../messages/entities/group-message.entity';
import { GroupLogService } from 'src/modules/audit/services/group.log.service';
import { SessionManagementService } from 'src/modules/security/services/session-management.service';
import { DataSource, QueryRunner } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';
import { GroupNotificationService } from 'src/modules/groups/services/group-notification.service';
import { MembersService } from 'src/modules/members/services/members.service';
import { MailService } from 'src/core/mail/mail.service';
import { AllocateMemberDto } from 'src/modules/groups/dto/allocate-member.dto';
import { ConfigService } from '@nestjs/config';
import {
  GroupChangeType,
  GroupLogAction,
} from 'src/modules/audit/enums/group-log-action.enum';
import { PairChatService } from './pair-chat.service';
import { PrivateMessageService } from 'src/modules/messages/services/pvt-messages.service';
import { PrivateMessage } from 'src/modules/messages/entities/private-message.entity';

@Injectable()
export class GroupUserService {
  constructor(
    private readonly messageThreadService: MessageThreadService,
    private readonly groupService: GroupsService,
    private readonly groupMemberService: GroupMembersService,
    private readonly groupMessageService: GroupMessagesService,
    private readonly groupEncryptionService: GroupEncryptionService,
    private readonly groupLogService: GroupLogService,
    private readonly sessionStateService: SessionManagementService,
    private readonly groupNotificationService: GroupNotificationService,
    private readonly memberService: MembersService,
    private readonly configService: ConfigService,
    private readonly mailService: MailService,
    private readonly pairChatService: PairChatService,
    private readonly privateMessageService: PrivateMessageService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {}

  async getUserGroups(memberId: number) {
    // Get all group message threads for the user
    const groupThreads = await this.messageThreadService.getMemberThreads(
      memberId,
      ChatType.GROUP,
    );

    // Get all private message threads for the user
    const privateThreads = await this.messageThreadService.getMemberThreads(
      memberId,
      ChatType.PRIVATE,
    );

    // Initialize collections
    const users: Set<any> = new Set();
    const groupMembers: any[] = []; // Only group membership data with user references
    const groupThreadsData: any[] = [];
    const privateThreadsData: any[] = [];

    // Add the current user to users collection
    const currentUser = await this.memberService.findOne(memberId);
    if (currentUser) {
      users.add(
        JSON.stringify({
          id: currentUser.id,
          name: currentUser.name,
          imageUrl: currentUser.imageUrl,
          phoneNo: currentUser.phoneNo,
        }),
      );
    }

    // Process Group Threads
    if (groupThreads.length > 0) {
      const groupsData = await Promise.all(
        groupThreads.map(async (thread) => {
          // Get group details
          const group = await this.groupService.findOne(thread.targetId);

          if (!group || !group.isActive) return null;

          // Check if user is still an active member
          const membership = await this.groupMemberService.findActiveMembership(
            memberId,
            thread.targetId,
          );

          if (!membership) return null;

          let lastMessage: GroupMessage | null = null;
          const groupLastSeq = await this.groupLogService.getLatestLog(
            group.id,
          );

          if (thread.lastMessageId) {
            lastMessage = await this.groupMessageService.findOneMessage(
              thread.lastMessageId,
            );
          }

          // Get group members
          const groupMemberDetails =
            await this.groupMemberService.getGroupMemberDetails(
              thread.targetId,
            );

          // Add group members to collections
          groupMemberDetails.members.forEach((member: any) => {
            // Add to users collection (unique users with full details)
            users.add(
              JSON.stringify({
                id: member.id,
                name: member.name,
                imageUrl: member.imageUrl,
                phoneNo: member.phoneNo,
              }),
            );

            // Add to group members collection (only membership data + user reference)
            groupMembers.push({
              groupMemberId: member.groupMemberId,
              groupId: thread.targetId,
              userId: member.id, // Reference to user in users array
              joinedAt: member.joinedAt,
              isMute: member.isMute,
              isActive: true,
            });
          });

          // Add to group threads collection
          const groupThreadData = {
            id: group.id,
            threadId: thread.id,
            type: ChatType.GROUP,
            name: group.name,
            imageUrl: group.imageUrl,
            memberCount: groupMemberDetails.members.length,
            lastMessage: lastMessage
              ? {
                  id: lastMessage.id,
                  encryptedContent: lastMessage.encryptedContent,
                  nonce: lastMessage.nonce,
                  groupKeyVersion: lastMessage.groupKeyVersion,
                  sentAt: lastMessage.sentAt,
                  senderId: lastMessage.sender?.id,
                  senderName: lastMessage.sender?.name,
                  seq: lastMessage.seq,
                }
              : null,
            welcomeMessage: thread.welcomeMessage || null,
            unreadCount: thread.unreadCount,
            isMuted: thread.isMuted,
            isPinned: thread.isPinned,
            lastUpdated: thread.updatedAt ?? group.updatedAt,
            syncState: {
              lastSyncedSeq: thread.syncState?.lastSyncedSeq || 0,
              lastAckedSeq: thread.syncState?.lastAckedSeq || 0,
              hasPending: thread.syncState?.hasPending || false,
            },
            lastMessageSeq: lastMessage?.seq || 0,
            groupLastSeq: groupLastSeq?.seq || 0,
            createdAt: group.createdAt,
            updatedAt: group.updatedAt,
            isActive: group.isActive,
          };

          groupThreadsData.push(groupThreadData);
          return groupThreadData;
        }),
      );
    }

    // Process Private Threads
    if (privateThreads.length > 0) {
      const privateData = await Promise.all(
        privateThreads.map(async (thread) => {
          // Get the other participant details
          const otherMember = await this.memberService.findOne(thread.targetId);
          if (!otherMember) return null;

          // Add other member to users collection
          users.add(
            JSON.stringify({
              id: otherMember.id,
              name: otherMember.name,
              imageUrl: otherMember.imageUrl,
              phoneNo: otherMember.phoneNo,
            }),
          );

          let lastMessage: PrivateMessage | null = null;
          if (thread.lastMessageId) {
            lastMessage = await this.privateMessageService.findOneMessage(
              thread.lastMessageId,
            );
          }

          // Generate a consistent private chat ID
          const participantIds = [memberId, thread.targetId].sort(
            (a, b) => a - b,
          );
          const privateChatId = `private_${participantIds[0]}_${participantIds[1]}`;

          // Add to private threads collection
          const privateThreadData = {
            id: privateChatId,
            threadId: thread.id,
            type: 'private',
            participantId: thread.targetId,
            lastMessage: lastMessage
              ? {
                  id: lastMessage.id,
                  seq: lastMessage.seq,
                  encryptedContent: lastMessage.encryptedContent,
                  caption: lastMessage.caption,
                  ephemeralPublicKey: lastMessage.ephemeralPublicKey,
                  messageIndex: lastMessage.messageIndex,
                  previousChainLength: lastMessage.previousChainLength,
                  chainKeyVersion: lastMessage.chainKeyVersion,
                  sentAt: lastMessage.sentAt,
                  deliveredAt: lastMessage.deliveredAt,
                  acknowledged: lastMessage.acknowledged,
                  senderId: lastMessage.senderId,
                  receiverId: lastMessage.receiverId,
                  senderName: lastMessage.sender?.name,
                  receiverName: lastMessage.receiver?.name,
                  hasMedia: !!lastMessage.file,
                  mediaFile: lastMessage.file
                    ? {
                        id: lastMessage.file.id,
                        fileName: lastMessage.file.filename,
                        mimeType: lastMessage.file.mimeType,
                        fileSize: lastMessage.file.fileSize,
                        encryptedUrl: lastMessage.file.encryptionKeys,
                      }
                    : null,
                }
              : null,
            unreadCount: thread.unreadCount,
            isMuted: thread.isMuted,
            isPinned: thread.isPinned,
            lastUpdated: thread.updatedAt,
            syncState: {
              lastSyncedSeq: thread.syncState?.lastSyncedSeq || 0,
              lastAckedSeq: thread.syncState?.lastAckedSeq || 0,
              hasPending: thread.syncState?.hasPending || false,
            },
            lastMessageSeq: lastMessage?.seq || 0,
            lastSeenSeq: thread.syncState?.lastAckedSeq || 0,
            createdAt: thread.createdAt,
            updatedAt: thread.updatedAt,
            isActive: true,
          };

          privateThreadsData.push(privateThreadData);
          return privateThreadData;
        }),
      );
    }

    // Convert users Set back to array and parse JSON
    const uniqueUsers = Array.from(users).map((userStr) =>
      JSON.parse(userStr as string),
    );

    // Sort threads by priority (pinned first, then by last update)
    const sortThreads = (threads: any[]) => {
      return threads
        .filter((thread) => thread !== null)
        .sort((a, b) => {
          // Pinned threads first
          if (a.isPinned && !b.isPinned) return -1;
          if (!a.isPinned && b.isPinned) return 1;

          // Then sort by last update time (most recent first)
          return (
            new Date(b.lastUpdated).getTime() -
            new Date(a.lastUpdated).getTime()
          );
        });
    };

    return {
      users: uniqueUsers,
      groupMembers: groupMembers,
      groupThreads: sortThreads(groupThreadsData),
      privateThreads: sortThreads(privateThreadsData),
      summary: {
        totalUsers: uniqueUsers.length,
        totalGroups: groupThreadsData.filter((g) => g !== null).length,
        totalPrivateChats: privateThreadsData.filter((p) => p !== null).length,
        totalGroupMembers: groupMembers.length,
      },
    };
  }

  async allocateGroupMembers(
    dto: AllocateMemberDto,
    userId: string,
  ): Promise<any[]> {
    const appName = this.configService.get<string>('APP_NAME', 'ChatApp');
    const { groupId, memberIds: targetMemberIds, adminSecretKey } = dto;

    if (!adminSecretKey) {
      throw new Error('Admin secret key is required for encryption operations');
    }

    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Use group service helper methods
      const group = await this.groupService.findGroupWithRelations(
        groupId,
        queryRunner,
      );
      if (!group) {
        throw new NotFoundException(`Group with ID ${groupId} not found`);
      }
      if (Number(group.createdBy) !== Number(userId)) {
        throw new ForbiddenException(`Not authorized to allocate members`);
      }

      // Get member changes using group member service
      const memberChanges =
        await this.groupMemberService.calculateMemberChanges(
          groupId,
          targetMemberIds,
          queryRunner,
        );

      const { membersToAdd, membersToRemove, existingMemberIds } =
        memberChanges;

      // Process removals using group member service
      await this.groupMemberService.removeMembers(
        groupId,
        membersToRemove,
        queryRunner,
      );

      // Process additions using group member service
      const { addedMembers, newMemberDetails } =
        await this.groupMemberService.addMembers(
          groupId,
          membersToAdd,
          queryRunner,
        );

      // Handle key rotation
      let newKeyVersion: number | null = null;
      if (membersToAdd.length > 0) {
        const existingActiveMemberIds = existingMemberIds.filter(
          (id) => !membersToRemove.includes(id),
        );

        newKeyVersion =
          await this.groupEncryptionService.allocateMembersWithKeyRotation(
            groupId,
            adminSecretKey,
            membersToAdd,
            existingActiveMemberIds,
          );

        // Log key version changes
        await this.logKeyVersionChanges(
          groupId,
          existingActiveMemberIds,
          membersToAdd,
          group.currentKeyVersion,
          newKeyVersion,
        );
      }

      // Create message threads for new members
      await this.createMessageThreadsForNewMembers(
        groupId,
        membersToAdd,
        existingMemberIds.filter((id) => !membersToRemove.includes(id)),
        queryRunner,
      );

      // Commit transaction first
      await queryRunner.commitTransaction();

      // Get updated member list using group member service
      const updatedGroupMembers =
        await this.groupMemberService.getActiveMembersWithDetails(groupId);

      // Send notifications (outside transaction)
      await this.handlePostAllocationNotifications(
        group,
        newMemberDetails,
        membersToAdd,
        membersToRemove,
        existingMemberIds,
        newKeyVersion,
        appName,
      );

      return updatedGroupMembers;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  private async createMessageThreadsForNewMembers(
    groupId: number,
    newMemberIds: number[],
    existingMemberIds: number[],
    queryRunner: QueryRunner,
  ): Promise<void> {
    for (const newMemberId of newMemberIds) {
      // Create group thread for new member
      await this.messageThreadService.findOrCreateThreadWithTransaction(
        newMemberId,
        ChatType.GROUP,
        groupId,
        queryRunner,
        'You have been added to this group',
      );

      // Create private threads with existing members
      for (const existingMemberId of existingMemberIds) {
        // 1️⃣ Find or create PairChat
        const pairChatId = await this.pairChatService.findOrCreatePairChat(
          newMemberId,
          existingMemberId,
          queryRunner,
        );

        // 2️⃣ Check if session already exists using pairChatId
        const hasActiveSession =
          await this.sessionStateService.hasActiveSession(
            pairChatId,
            queryRunner,
          );

        if (!hasActiveSession) {
          // 3️⃣ Get member names for welcome messages
          const memberName =
            await this.memberService.getMemberName(existingMemberId);
          const newMemberName =
            await this.memberService.getMemberName(newMemberId);

          // 4️⃣ Create private threads
          await this.messageThreadService.findOrCreateThreadWithTransaction(
            newMemberId,
            ChatType.PRIVATE,
            existingMemberId,
            queryRunner,
            `Start your conversation with ${memberName}`,
          );

          await this.messageThreadService.findOrCreateThreadWithTransaction(
            existingMemberId,
            ChatType.PRIVATE,
            newMemberId,
            queryRunner,
            `Start your conversation with ${newMemberName}`,
          );

          // 5️⃣ Create session state using pairChatId
          await this.sessionStateService.createSessionBetweenMembers(
            pairChatId,
            queryRunner,
          );
        }
      }

      // Create private threads between new members themselves
      for (const otherNewMemberId of newMemberIds) {
        if (newMemberId !== otherNewMemberId) {
          // 1️⃣ Find or create PairChat
          const pairChatId = await this.pairChatService.findOrCreatePairChat(
            newMemberId,
            otherNewMemberId,
            queryRunner,
          );

          // 2️⃣ Check if session already exists
          const hasActiveSession =
            await this.sessionStateService.hasActiveSession(
              pairChatId,
              queryRunner,
            );

          if (!hasActiveSession) {
            // 3️⃣ Get member names for welcome messages
            const memberName =
              await this.memberService.getMemberName(otherNewMemberId);
            const newMemberName =
              await this.memberService.getMemberName(newMemberId);

            // 4️⃣ Create threads for both directions
            await this.messageThreadService.findOrCreateThreadWithTransaction(
              newMemberId,
              ChatType.PRIVATE,
              otherNewMemberId,
              queryRunner,
              `Start your conversation with ${memberName}`,
            );

            await this.messageThreadService.findOrCreateThreadWithTransaction(
              otherNewMemberId,
              ChatType.PRIVATE,
              newMemberId,
              queryRunner,
              `Start your conversation with ${newMemberName}`,
            );

            // 5️⃣ Create session state using pairChatId
            await this.sessionStateService.createSessionBetweenMembers(
              pairChatId,
              queryRunner,
            );
          }
        }
      }
    }
  }

  private async logKeyVersionChanges(
    groupId: number,
    existingActiveMemberIds: number[],
    membersToAdd: number[],
    oldKeyVersion: number,
    newKeyVersion: number,
  ): Promise<void> {
    const allAffectedMemberIds = [...existingActiveMemberIds, ...membersToAdd];

    for (const memberId of allAffectedMemberIds) {
      await this.groupLogService.logChange(
        groupId,
        GroupChangeType.KEY,
        GroupLogAction.KEY_UPDATE,
        {
          memberId,
          changedField: 'key_version',
          oldValue: oldKeyVersion.toString(),
          newValue: newKeyVersion.toString(),
        },
      );
    }
  }

  private async handlePostAllocationNotifications(
    group: any,
    newMemberDetails: any[],
    membersToAdd: number[],
    membersToRemove: number[],
    existingMemberIds: number[],
    newKeyVersion: number | null,
    appName: string,
  ): Promise<void> {
    try {
      // Send email notifications
      for (const member of newMemberDetails) {
        if (member.email && !member.isVerified) {
          await this.mailService.sendMemberNotification(
            member.email,
            member.name,
            group.organization.name,
            group.name,
            appName,
            member.phoneNo,
          );
        }
      }

      // Emit events to all relevant parties
      if (membersToAdd.length > 0 || membersToRemove.length > 0) {
        const latestLog = await this.groupLogService.getLatestLog(group.id);
        const latestSeq = latestLog?.seq || 0;

        const allRelevantMemberIds = [...membersToAdd, ...membersToRemove];
        const verifiedMembers =
          await this.memberService.getVerifiedMembersWithSignedUrls(
            allRelevantMemberIds,
          );

        // Emit to new members
        if (membersToAdd.length > 0) {
          const verifiedNewMemberIds = verifiedMembers
            .filter((m) => membersToAdd.includes(m.id))
            .map((m) => m.id);

          if (verifiedNewMemberIds.length > 0) {
            await this.groupNotificationService.emitGroupInfoToNewMembers(
              group,
              verifiedNewMemberIds,
              verifiedMembers,
              latestSeq,
            );
          }
        }

        // Emit to existing members
        const existingActiveMemberIds = existingMemberIds.filter(
          (id) => !membersToRemove.includes(id),
        );

        if (existingActiveMemberIds.length > 0) {
          const verifiedExistingMemberIds =
            await this.memberService.getVerifiedMemberIds(
              existingActiveMemberIds,
            );

          if (verifiedExistingMemberIds.length > 0) {
            await this.groupNotificationService.emitGroupUpdatesToExistingMembers(
              group,
              verifiedExistingMemberIds,
              {
                newMembers: verifiedMembers.filter((m) =>
                  membersToAdd.includes(m.id),
                ),
                leftMembers: verifiedMembers.filter((m) =>
                  membersToRemove.includes(m.id),
                ),
                newKeyVersion,
              },
              latestSeq,
            );
          }
        }
      }
    } catch (notificationError) {
      console.error('Failed to send notifications', notificationError);
    }
  }

  async getNextSequenceNumber(
    messageType: 'group' | 'private',
    contextId: number,
    queryRunner: QueryRunner,
  ): Promise<number> {
    if (messageType === 'group') {
      return this.groupMessageService.getNextSequenceNumber(
        contextId,
        queryRunner,
      );
    } else {
      const { member1Id, member2Id } =
        await this.pairChatService.getChatMembers(contextId, queryRunner);
      return this.privateMessageService.getNextSequenceNumber(
        member1Id,
        member2Id,
        queryRunner,
      );
    }
  }
}
