import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MediaService } from './services/media.service';
import { MediaUploadService } from './services/media-upload.service';
import { MediaDownloadService } from './services/media-download.service';
import { MediaStreamingService } from './services/media-streaming.service';
import { MediaController } from './controllers/media.controller';
import { MediaDownloadController } from './controllers/media-download.controller';
import { MediaStreamingController } from './controllers/media-streaming.controller';

/**
 * Media Module
 *
 * Handles media file management including upload, download,
 * streaming, compression, encryption, thumbnail generation,
 * and audio waveform processing with visualization.
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([
      // Add media-related entities here when created
    ]),
  ],
  controllers: [
    MediaController,
    MediaDownloadController,
    MediaStreamingController,
  ],
  providers: [
    MediaService,
    MediaUploadService,
    MediaDownloadService,
    MediaStreamingService,
  ],
  exports: [
    MediaService,
    MediaUploadService,
    MediaDownloadService,
    MediaStreamingService,
  ],
})
export class MediaModule {}
