import { Injectable, Logger } from '@nestjs/common';
import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import {
  OTP_DLQ_EXCHANGE,
  OTP_DLQ_QUEUE,
  OTP_FAILED_ROUTING_KEY,
} from '../../../common/constants';
import { MailService } from '../../../core/mail/mail.service';

interface DlqOtpMessage {
  phoneNumber: string;
  otp: string;
  createdAt: string;
  failureReason?: string;
  failedAt?: string;
  originalQueue?: string;
}

@Injectable()
export class OtpDlqConsumer {
  constructor(private readonly mailService: MailService) {}
  private readonly logger = new Logger(OtpDlqConsumer.name);

  @RabbitSubscribe({
    exchange: OTP_DLQ_EXCHANGE,
    routingKey: OTP_FAILED_ROUTING_KEY,
    queue: OTP_DLQ_QUEUE,
    queueOptions: {
      durable: true,
    },
  })
  async handleOtpFailure(message: DlqOtpMessage) {
    this.logger.error(
      `OTP delivery failed permanently for ${message.phoneNumber}`,
      {
        phoneNumber: message.phoneNumber,
        otp: message.otp,
        originalCreatedAt: message.createdAt,
        failureReason: message.failureReason || 'Unknown failure',
        failedAt: message.failedAt,
        originalQueue: message.originalQueue,
      },
    );
    await this.mailService.sendSystemAlert(
      'OTP Delivery Failure',
      `OTP delivery failed permanently for ${message.phoneNumber}. Reason: ${message.failureReason}`,
    );
  }
}
