import { BaseEvent } from './base.event';
import { EVENT_NAMES } from '../constants/event-names';
import {
  MemberNotificationInfo,
  PushNotification,
} from 'src/infrastructure/notification/services/notification.service';

export class SendPrivateMessageRequestedEvent {
  constructor(
    public payload: {
      chatType: string;
      chatId: string;
      senderId: number;
      receiverId: number;
      content: string;
      messageType: string;
      attachments: any[];
      replyToMessageId?: string;
      metadata?: any;
      socketId: string;
    },
  ) {}
}

export class PrivateMessageReadRequestedEvent {
  constructor(
    public payload: {
      chatId: string;
      readerId: number;
      messageIds: number[];
      readAt: Date;
    },
  ) {}
}

export class DeletePrivateMessageForMeRequestedEvent {
  constructor(
    public payload: {
      chatId: string;
      deleterId: number;
      messageId: number;
      deletedAt: Date;
    },
  ) {}
}

export class DeletePrivateMessagesForEveryoneRequestedEvent {
  constructor(
    public payload: {
      chatId: string;
      deleterId: number;
      messageIds: number[];
      deletedAt: Date;
    },
  ) {}
}

export class PrivateMessageSentEvent extends BaseEvent {
  readonly name = EVENT_NAMES.PRIVATE_MESSAGE_SENT;

  constructor(
    public readonly messageId: number,
    public readonly senderId: number,
    public readonly receiverId: number,
    public readonly memberNotificationInfo: MemberNotificationInfo[],
    public readonly notificationPayload: PushNotification,
    public readonly retryCount: number,
  ) {
    super();
  }
}

export class PrivateMessageDeliveryAttemptEvent extends BaseEvent {
  readonly name = EVENT_NAMES.PRIVATE_MESSAGE_DELIVERY_ATTEMPT;

  constructor(public readonly messageId: number) {
    super();
  }
}

export class PrivateMessageDeliveryFailedEvent extends BaseEvent {
  readonly name = EVENT_NAMES.PRIVATE_MESSAGE_DELIVERY_FAILED;

  constructor(
    public readonly messageId: number,
    public readonly reason: string,
  ) {
    super();
  }
}

export class PrivateMessageDeliverySuccessEvent extends BaseEvent {
  readonly name = EVENT_NAMES.PRIVATE_MESSAGE_DELIVERY_SUCCESS;

  constructor(
    public readonly messageId: number,
    public readonly deliveredToCount: number,
  ) {
    super();
  }
}
