import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CoreModule } from 'src/core/core.module';
import { SecurityModule } from 'src/modules/security/security.module';
import { PairChat } from './entities/pair-chats.entity';
import { GroupUserService } from './services/group-user.service';
import { GroupsModule } from 'src/modules/groups/groups.module';
import { AuditModule } from 'src/modules/audit/audit.module';
import { StorageModule } from 'src/core/storage/storage.module';
import { MembersModule } from 'src/modules/members/members.module';
import { UserGroupsController } from './controllers/user-groups.controller';
import { MessagesModule } from 'src/modules/messages/messages.module';
import { PairChatService } from './services/pair-chat.service';

/**
 * P2P Module
 *
 * Handles peer-to-peer messaging functionality including
 * end-to-end encryption, key exchange, session establishment,
 * and secure messaging using Signal Protocol.
 */
@Module({
  imports: [
    TypeOrmModule.forFeature([PairChat]),
    SecurityModule,
    CoreModule,
    MembersModule,
    GroupsModule,
    AuditModule,
    StorageModule,
    MessagesModule,
  ],
  controllers: [UserGroupsController],
  providers: [GroupUserService, PairChatService],
  exports: [],
})
export class P2pModule {}
