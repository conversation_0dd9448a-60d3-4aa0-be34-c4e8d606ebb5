import { IsNotEmpty, IsString, <PERSON><PERSON>eng<PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class ChangePasswordDto {
  @ApiProperty({
    description: 'Current password of the user',
    minLength: 6,
    example: 'oldPassword123',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  currentPassword: string;

  @ApiProperty({
    description: 'New password to set',
    minLength: 6,
    example: 'newPassword456',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  newPassword: string;

  @ApiProperty({
    description: 'Confirm the new password',
    minLength: 6,
    example: 'newPassword456',
  })
  @IsNotEmpty()
  @IsString()
  @MinLength(6)
  confirmNewPassword: string;
}
