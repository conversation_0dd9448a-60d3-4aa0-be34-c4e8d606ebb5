import { PairChat } from 'src/modules/chat/p2p/entities/pair-chats.entity';
import { OrgMember } from '../../members/entities/org-member.entity';
import {
  Entity,
  Column,
  PrimaryGeneratedColumn,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity('session_states')
export class SessionStateEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  pairChatId: number;

  @Column({ default: true })
  isActive: boolean;

  @Column({ nullable: true, type: 'timestamp' })
  expiredAt?: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @Column({ nullable: true, type: 'timestamp' })
  lastMessageAt?: Date;

  @Column({ default: 'p2p' })
  sessionType: 'p2p' | 'group';

  @ManyToOne(() => PairChat, (pairChat) => pairChat.sessions)
  @JoinColumn({ name: 'pairChatId' })
  pairChat: PairChat;
}
