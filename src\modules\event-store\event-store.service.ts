import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Not, IsNull } from 'typeorm';
import { BaseEvent } from '../../common/events/base.event';
import { StoredEvent } from './entities/stored-event.entity';

@Injectable()
export class EventStoreService {
  private readonly logger = new Logger(EventStoreService.name);

  constructor(
    @InjectRepository(StoredEvent)
    private readonly storedEventRepository: Repository<StoredEvent>,
  ) {}

  async store(
    event: BaseEvent,
    metadata?: Record<string, any>,
  ): Promise<StoredEvent> {
    try {
      const storedEvent = this.storedEventRepository.create({
        eventName: event.name,
        eventData: event,
        metadata: metadata || {},
        timestamp: event.timestamp,
        version: 1,
      });

      const saved = await this.storedEventRepository.save(storedEvent);

      this.logger.debug(`Stored event: ${event.name}`, {
        eventId: saved.id,
        eventName: event.name,
      });

      return saved;
    } catch (error) {
      this.logger.error(`Failed to store event ${event.name}:`, error);
      throw error;
    }
  }

  async getEvents(
    eventName?: string,
    fromTimestamp?: Date,
    toTimestamp?: Date,
    limit: number = 100,
  ): Promise<StoredEvent[]> {
    const query = this.storedEventRepository.createQueryBuilder('event');

    if (eventName) {
      query.andWhere('event.eventName = :eventName', { eventName });
    }

    if (fromTimestamp) {
      query.andWhere('event.timestamp >= :fromTimestamp', { fromTimestamp });
    }

    if (toTimestamp) {
      query.andWhere('event.timestamp <= :toTimestamp', { toTimestamp });
    }

    return query.orderBy('event.timestamp', 'DESC').limit(limit).getMany();
  }

  async getEventById(id: string): Promise<StoredEvent | null> {
    return this.storedEventRepository.findOne({ where: { id } });
  }

  async getEventsByCorrelationId(
    correlationId: string,
  ): Promise<StoredEvent[]> {
    return this.storedEventRepository.find({
      where: { correlationId },
      order: { timestamp: 'ASC' },
    });
  }

  async markEventAsProcessed(
    eventId: string,
    processedBy: string,
  ): Promise<void> {
    await this.storedEventRepository.update(eventId, {
      processedAt: new Date(),
      processedBy,
    });
  }

  async markEventAsFailed(
    eventId: string,
    error: string,
    retryCount: number = 0,
  ): Promise<void> {
    await this.storedEventRepository.update(eventId, {
      failedAt: new Date(),
      errorMessage: error,
      retryCount,
    });
  }

  async getFailedEvents(limit: number = 50): Promise<StoredEvent[]> {
    return this.storedEventRepository.find({
      where: { failedAt: Not(IsNull()) },
      order: { failedAt: 'DESC' },
      take: limit,
    });
  }

  async getUnprocessedEvents(limit: number = 100): Promise<StoredEvent[]> {
    return this.storedEventRepository.find({
      where: {
        processedAt: IsNull(),
        failedAt: IsNull(),
      },
      order: { timestamp: 'ASC' },
      take: limit,
    });
  }
}
