import { Logger } from '@nestjs/common';
import {
  IEventHandler,
  IEventHandlerMetadata,
} from './event.handler.interface';
import { BaseEvent } from '../../../common/events/base.event';

export abstract class BaseEventHandler<T extends BaseEvent = BaseEvent>
  implements IEventHandler<T>
{
  protected readonly logger = new Logger(this.constructor.name);

  abstract handle(event: T): Promise<void> | void;

  protected getMetadata(): IEventHandlerMetadata {
    return {
      eventName: this.constructor.name.replace('Handler', '').toLowerCase(),
      priority: 0,
      timeout: 30000, // 30 seconds default
      retryCount: 3,
      retryDelay: 1000, // 1 second
    };
  }

  protected async executeWithTimeout<R>(
    operation: () => Promise<R>,
    timeoutMs: number = this.getMetadata().timeout || 30000,
  ): Promise<R> {
    return Promise.race([
      operation(),
      new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Operation timeout')), timeoutMs),
      ),
    ]);
  }

  protected async executeWithRetry<R>(
    operation: () => Promise<R>,
    maxRetries: number = this.getMetadata().retryCount || 3,
    delay: number = this.getMetadata().retryDelay || 1000,
  ): Promise<R> {
    let lastError: Error = new Error('Operation failed after all retries');

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;
        this.logger.warn(
          `Attempt ${attempt + 1}/${maxRetries + 1} failed: ${error.message}`,
        );

        if (attempt < maxRetries) {
          await this.sleep(delay * Math.pow(2, attempt)); // Exponential backoff
        }
      }
    }

    throw lastError;
  }

  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}
