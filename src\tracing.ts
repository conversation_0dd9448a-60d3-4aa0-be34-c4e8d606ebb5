import { NodeSDK } from '@opentelemetry/sdk-node';
import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-grpc';
import { resourceFromAttributes } from '@opentelemetry/resources';

// Read from env, fallback to default
const tempoUrl = process.env.TEMPO_OTLP_GRPC_URL || 'http://localhost:4317';

const traceExporter = new OTLPTraceExporter({
  url: tempoUrl,
});

export const sdk = new NodeSDK({
  traceExporter,
  resource: resourceFromAttributes({
    [ATTR_SERVICE_NAME]: 'talkio-chat-service',
  }),
  instrumentations: [getNodeAutoInstrumentations()],
});
