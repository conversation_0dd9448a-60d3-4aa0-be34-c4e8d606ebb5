services:
  redis:
    image: redis:latest
    container_name: redis-server
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data
    restart: unless-stopped
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}

  redisinsight:
    image: redislabs/redisinsight:1.13.1
    container_name: redisinsight
    ports:
      - '8001:8001'
    volumes:
      - redisinsight-data:/db
    restart: unless-stopped
    depends_on:
      - redis

  rabbitmq:
    image: rabbitmq:3-management
    container_name: rabbitmq
    ports:
      - '5672:5672'
      - '15672:15672'
    volumes:
      - rabbitmq-data:/var/lib/rabbitmq
    environment:
      - RABBITMQ_DEFAULT_USER=${RABBITMQ_USER}
      - RABBITMQ_DEFAULT_PASS=${RABBITMQ_PASSWORD}
    restart: unless-stopped

  prometheus:
    image: prom/prometheus:latest
    container_name: prometheus
    ports:
      - '9090:9090'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
    restart: unless-stopped
    depends_on:
      - rabbitmq
      - redis

  grafana:
    image: grafana/grafana:latest
    container_name: grafana
    ports:
      - '4000:4000'
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin # Change this in production
      - GF_AUTH_ANONYMOUS_ENABLED=true # Optional: allow anonymous access
      - GF_SERVER_HTTP_PORT=4000
    volumes:
      - grafana-data:/var/lib/grafana
    restart: unless-stopped
    depends_on:
      - prometheus
      - tempo

  tempo:
    image: grafana/tempo:latest
    container_name: tempo
    ports:
      - '3200:3200' # Tempo HTTP API
      - '4317:4317' # OTLP gRPC receiver (for traces)
    volumes:
      - tempo-data:/var/tempo
      - ./monitoring/tempo.yaml:/etc/tempo.yaml
    command:
      - '--config.file=/etc/tempo.yaml'
    restart: unless-stopped

volumes:
  redis-data:
    driver: local
  redisinsight-data:
    driver: local
  rabbitmq-data:
    driver: local
  grafana-data:
    driver: local
  tempo-data:
    driver: local
