import {
  Injectable,
  NotFoundException,
  UnauthorizedException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as argon2 from 'argon2';
import { RedisService } from '../../../infrastructure/redis/services/redis.service';
import { JwtAdminPayload } from '../interfaces/jwt-payload.interface';
import { AdminLoginDto, AdminResponseDto, LoginResponseDto } from '../dto/';
import { AdminUserType, UserType } from '../../../common/types/user-type';
import { UsersService } from '../../users/services/users.service';
import { Request } from 'express';
import { USER_TYPE } from 'src/common/constants/user-type.constants';
import {
  AdminUser,
  AuthenticatedUser,
  MemberUser,
} from 'src/common/types/authenticate-user.types';
import { isAdminUser, isMemberUser } from 'src/common/utils/user-type.util';
import { TokenService } from './token.service';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    private readonly userService: UsersService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly tokenService: TokenService,
  ) {}

  /**
   * Authenticate an admin user
   */
  async login(
    loginDto: AdminLoginDto,
    req: Request,
  ): Promise<LoginResponseDto> {
    // Find the user by username (assumed to be unique)
    const user = await this.userService.findOne(loginDto.username);

    if (!user) {
      throw new NotFoundException('User not found');
    }

    let userType: AdminUserType | undefined;

    if (user.roleId === 1) {
      userType = USER_TYPE.PRODUCT_ADMIN;
    } else if (user.roleId === 2) {
      userType = USER_TYPE.ORG_ADMIN;
    }

    if (!userType) {
      throw new UnauthorizedException('Invalid user role');
    }

    // Validate password
    const isPasswordValid = await this.verifyPassword(
      user.password,
      loginDto.password,
    );

    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const payload: JwtAdminPayload = {
      sub: user.id,
      type: userType,
      orgId: user.orgId,
      roleId: user.roleId,
    };

    // Generate tokens
    const { accessToken, refreshToken, tokenId } =
      await this.tokenService.generateTokens(payload);

    await this.tokenService.storeRefreshToken(
      userType,
      user.id,
      refreshToken,
      tokenId,
      req,
    );

    // Return user details and tokens
    return {
      accessToken,
      refreshToken,
    };
  }

  async getUserDetails(
    user: AuthenticatedUser,
  ): Promise<AdminResponseDto | any> {
    if (isAdminUser(user)) {
      return this.getAdminUserDetails(user);
    }

    if (isMemberUser(user)) {
      return this.getMemberUserDetails(user);
    }

    throw new Error('Invalid user type');
  }

  /**
   * Refresh access token using a valid refresh token
   */

  /**
   * Logout an admin user by blacklisting their refresh token
   */
  async logout(
    userId: number,
    userType: string,
    tokenId: string,
  ): Promise<void> {
    try {
      await this.redisService.removeRefreshToken(
        userType as UserType,
        userId,
        tokenId,
      );
    } catch (error) {
      this.logger.error(`Logout error: ${error.message}`);
      throw new BadRequestException('Could not complete logout');
    }
  }

  /**
   * Logout from all devices by removing all refresh tokens for a user
   */
  async logoutAll(userId: number, userType: string): Promise<void> {
    try {
      await this.redisService.removeAllRefreshTokensForUser(
        userType as UserType,
        userId,
      );
    } catch (error) {
      this.logger.error(`Logout all error: ${error.message}`);
      throw new BadRequestException(
        'Could not complete logout from all devices',
      );
    }
  }

  /**
   * Verify password using argon2
   */
  private async verifyPassword(
    hashedPassword: string,
    plainPassword: string,
  ): Promise<boolean> {
    try {
      return await argon2.verify(hashedPassword, plainPassword);
    } catch (error) {
      this.logger.error(`Password verification error: ${error.message}`);
      return false;
    }
  }

  private getAdminUserDetails(user: AdminUser): AdminResponseDto {
    const type =
      user.roleId === 1 ? USER_TYPE.PRODUCT_ADMIN : USER_TYPE.ORG_ADMIN;

    let encryptedAdminSecretKey: string | undefined;
    let adminSecretKeyNonce: string | undefined;
    let adminSecretKeySalt: string | undefined;

    if (user.roleId === 2) {
      encryptedAdminSecretKey = user.encryptedAdminSecretKey;
      adminSecretKeyNonce = user.adminSecretKeyNonce;
      adminSecretKeySalt = user.adminSecretKeySalt;
    }

    return {
      id: user.id,
      username: user.username,
      email: user.email,
      type,
      roleId: user.roleId,
      orgId: user.organization?.id,
      orgName: user.organization?.name,
      imageUrl: user?.imageUrl,
      ...(user.roleId === 2 && {
        encryptedAdminSecretKey,
        adminSecretKeyNonce,
        adminSecretKeySalt,
      }),
    };
  }

  private async getMemberUserDetails(user: MemberUser) {
    if (!user.orgId) {
      throw new Error('Member user must have orgId');
    }

    const admin = await this.userService.findById(user.createdBy);

    return {
      id: user.id,
      name: user.name,
      email: user.email,
      orgId: user.organization.id,
      orgName: user.organization.name,
      type: USER_TYPE.ORG_MEMBER,
      imageUrl: user?.imageUrl,
      encryptedPrivateKey: user.encryptedPrivateKey,
      memberPublicKey: user.publicKey,
      adminPublicKey: admin.publicKey,
    };
  }
}
