export enum GroupChangeType {
  META = 'meta', // Group metadata changes
  MEMBER = 'member', // Member-related changes
  KEY = 'key', // Group key changes
  PROFILE = 'profile', // Member profile changes
}

export enum GroupLogAction {
  CREATE = 'create', // Group creation
  UPDATE = 'update', // General update
  DELETE = 'delete', // Group deletion
  JOIN = 'join', // Member joined
  LEAVE = 'leave', // Member left
  MUTE = 'mute', // Member muted
  UNMUTE = 'unmute', // Member unmuted
  KEY_ROTATE = 'key_rotate', // Group key rotation
  KEY_UPDATE = 'key_update', // Group key update
  PROFILE_UPDATE = 'profile_update', // Member profile update
}
