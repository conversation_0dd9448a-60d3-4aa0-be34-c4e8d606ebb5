export function getSystemAlertTemplate(
  subject: string,
  message: string,
): string {
  return `
    <div style="font-family: Arial, sans-serif; padding: 20px; background-color: #f7f7f7;">
      <div style="max-width: 600px; margin: auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
        <div style="background-color: #d32f2f; color: white; padding: 16px 24px;">
          <h2 style="margin: 0;">🚨 System Alert</h2>
        </div>
        <div style="padding: 24px;">
          <h3 style="color: #333;">${subject}</h3>
          <p style="color: #555; font-size: 14px; line-height: 1.6;">
            ${message}
          </p>
          <p style="color: #999; font-size: 12px; margin-top: 30px;">This is an automated system notification. No action may be required unless specified.</p>
        </div>
      </div>
    </div>
  `;
}
