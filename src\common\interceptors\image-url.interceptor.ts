import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from '@nestjs/common';
import { Observable, from } from 'rxjs';
import { switchMap } from 'rxjs/operators';
import { StorageService } from '../../core/storage/storage.service';
import { ConfigService } from '@nestjs/config';
import { Reflector } from '@nestjs/core';
import { PERMANENT_URLS } from '../constants/interceptor.constants';

/**
 * Interceptor to transform image and file paths into signed URLs
 */
@Injectable()
export class ImageUrlInterceptor implements NestInterceptor {
  private readonly logger = new Logger(ImageUrlInterceptor.name);
  private readonly imageUrlExpiry: number;
  private readonly permanentExpiry: number;

  constructor(
    private readonly storageService: StorageService,
    private readonly configService: ConfigService,
    private readonly reflector: Reflector,
  ) {
    this.imageUrlExpiry = this.configService.get<number>(
      'storage.imageUrlExpiry',
      86400,
    );

    // Very long expiration time for "permanent" URLs (10 years in seconds)
    this.permanentExpiry = this.configService.get<number>(
      'storage.permanentUrlExpiry',
      315360000,
    );
  }

  /**
   * Recursively processes objects to replace image and file paths with signed URLs
   * Fixed to preserve Date objects and handle TypeORM entities properly
   */
  private async processObject(
    obj: any,
    permanentFields: string[] = [],
    path: string = '',
  ): Promise<any> {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    // Handle Date objects - return as-is to preserve them
    if (obj instanceof Date) {
      return obj;
    }

    if (Array.isArray(obj)) {
      return Promise.all(
        obj.map((item, index) =>
          this.processObject(
            item,
            permanentFields,
            path ? `${path}[${index}]` : `[${index}]`,
          ),
        ),
      );
    }

    // Instead of spread operator, create result object more carefully
    const result: any = {};
    const promises: Promise<any>[] = [];

    // Get all enumerable properties from the object
    const keys = this.getAllKeys(obj);

    for (const key of keys) {
      try {
        const value = obj[key];
        const currentPath = path ? `${path}.${key}` : key;

        // Skip functions and undefined values
        if (typeof value === 'function' || value === undefined) {
          continue;
        }

        // Skip TypeORM internal properties
        if (this.isTypeORMInternal(key)) {
          continue;
        }

        // Handle Date objects - preserve them as-is
        if (value instanceof Date) {
          result[key] = value;
          continue;
        }

        if (typeof value === 'object' && value !== null) {
          promises.push(
            this.processObject(value, permanentFields, currentPath).then(
              (processed) => {
                result[key] = processed;
              },
            ),
          );
        } else if (
          // Check for fileUrl and imageUrl properties that contain relative paths
          (key === 'fileUrl' ||
            key === 'imageUrl' ||
            key.endsWith('FileUrl') ||
            key.endsWith('ImageUrl')) &&
          typeof value === 'string' &&
          value.length > 0 &&
          !value.startsWith('http')
        ) {
          // Check if this field should have a permanent URL
          const isPermanent = permanentFields.some((field) => {
            // Check exact match or wildcard patterns like "user.*" or "*.profileImage"
            if (field === '*' || field === currentPath) {
              return true;
            }

            // Handle wildcard at the beginning (e.g., "*.imageUrl")
            if (
              field.startsWith('*') &&
              currentPath.endsWith(field.substring(1))
            ) {
              return true;
            }

            // Handle wildcard at the end (e.g., "user.*")
            if (
              field.endsWith('*') &&
              currentPath.startsWith(field.substring(0, field.length - 1))
            ) {
              return true;
            }

            return false;
          });

          const expiryTime = isPermanent
            ? this.permanentExpiry
            : this.imageUrlExpiry;

          promises.push(
            this.storageService
              .generateSignedUrl(value, expiryTime)
              .then((url) => {
                result[key] = url;
              })
              .catch((error) => {
                this.logger.error(
                  `Failed to generate signed URL for ${value}: ${error.message}`,
                  error.stack,
                );
                // Keep original value on error
                result[key] = value;
              }),
          );
        } else {
          // Copy primitive values as-is
          result[key] = value;
        }
      } catch (error) {
        // Skip properties that throw errors when accessed
        this.logger.warn(
          `Skipping property ${key} due to error: ${error.message}`,
        );
        continue;
      }
    }

    await Promise.all(promises);
    return result;
  }

  /**
   * Get all relevant keys from an object, handling TypeORM entities properly
   */
  private getAllKeys(obj: any): string[] {
    const keys = new Set<string>();

    // Get own enumerable properties
    Object.keys(obj).forEach((key) => keys.add(key));

    // For TypeORM entities, also check prototype properties
    if (obj.constructor !== Object && obj.constructor !== Array) {
      try {
        const proto = Object.getPrototypeOf(obj);
        if (proto && proto !== Object.prototype) {
          Object.getOwnPropertyNames(proto).forEach((key) => {
            if (key !== 'constructor') {
              try {
                const descriptor = Object.getOwnPropertyDescriptor(proto, key);
                // Include getters that are likely entity properties
                if (
                  descriptor &&
                  descriptor.get &&
                  !this.isTypeORMInternal(key)
                ) {
                  keys.add(key);
                }
              } catch {
                // Skip inaccessible properties
              }
            }
          });
        }
      } catch {
        // Skip if prototype inspection fails
      }
    }

    return Array.from(keys);
  }

  /**
   * Check if a property key is a TypeORM internal property that should be skipped
   */
  private isTypeORMInternal(key: string): boolean {
    const internalProps = [
      '__entity',
      '__manager',
      '__connection',
      '__repository',
      'hasId',
      'save',
      'remove',
      'reload',
      'recover',
      'softRemove',
      'softRecover',
    ];
    return internalProps.includes(key) || key.startsWith('__');
  }

  /**
   * Intercepts the response and transforms image paths to URLs
   */
  async intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Promise<Observable<any>> {
    // Get permanent URL fields from controller metadata
    const permanentUrlFields =
      this.reflector.get<string[]>(PERMANENT_URLS, context.getHandler()) || [];

    return next.handle().pipe(
      switchMap((data) => {
        if (!data) {
          return from([data]);
        }

        return from(
          this.processObject(data, permanentUrlFields).catch((error) => {
            this.logger.error(
              `Error processing image URLs: ${error.message}`,
              error.stack,
            );
            return data; // Fall back to original data
          }),
        );
      }),
    );
  }
}
