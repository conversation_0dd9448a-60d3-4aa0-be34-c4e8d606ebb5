import { USER_TYPE } from '../constants/user-type.constants';
import {
  AdminUser,
  AuthenticatedUser,
  MemberUser,
} from '../types/authenticate-user.types';

export function isAdminUser(user: AuthenticatedUser): user is AdminUser {
  return (
    user.type === USER_TYPE.PRODUCT_ADMIN || user.type === USER_TYPE.ORG_ADMIN
  );
}

export function isMemberUser(user: AuthenticatedUser): user is MemberUser {
  return user.type === USER_TYPE.ORG_MEMBER;
}
