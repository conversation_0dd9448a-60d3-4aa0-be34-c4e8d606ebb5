import {
  <PERSON><PERSON><PERSON>,
  CreateDateColumn,
  <PERSON><PERSON>ty,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  ManyToOne,
  PrimaryGeneratedColumn,
} from 'typeorm';
import { MediaFile } from './media-file.entity';
import { OrgMember } from '../../members/entities/org-member.entity';

@Entity('media_keys')
export class MediaEncryptionKey {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  mediaId: number;

  @Column()
  recipientId: number;

  @Column({ type: 'text' })
  wrappedCek: string;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @ManyToOne(() => MediaFile, (media) => media.encryptionKeys, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'mediaId' })
  media: MediaFile;

  @ManyToOne(() => OrgMember, (member) => member.mediaKeys, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'recipientId' })
  recipient: OrgMember;
}
