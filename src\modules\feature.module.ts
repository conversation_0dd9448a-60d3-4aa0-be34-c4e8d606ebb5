import { Module } from '@nestjs/common';
import { UserModule } from './users/users.module';
import { AuthModule } from './auth/auth.module';
import { GroupsModule } from './groups/groups.module';
import { MembersModule } from './members/members.module';
import { MessagesModule } from './messages/messages.module';
import { OrganizationsModule } from './organization/organizations.module';
import { MediaModule } from './media/media.module';
import { SecurityModule } from './security/security.module';
import { SubscriptionModule } from './subscription/subscription.module';
import { AuditModule } from './audit/audit.module';
import { ChatModule } from './chat/chat.module';

@Module({
  imports: [
    UserModule,
    AuthModule,
    GroupsModule,
    MembersModule,
    MessagesModule,
    OrganizationsModule,
    MediaModule,
    SecurityModule,
    SubscriptionModule,
    AuditModule,
    ChatModule,
  ],
  exports: [
    UserModule,
    AuthModule,
    GroupsModule,
    MembersModule,
    MessagesModule,
    OrganizationsModule,
    MediaModule,
    SecurityModule,
    SubscriptionModule,
    AuditModule,
    ChatModule,
  ],
})
export class FeatureModule {}
