import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';
import { getOTPEmailTemplate } from './templates/otp-email.template';
import { getStaffCredentialsTemplate } from './templates/staff-credential.template';
import { SentMessageInfo, Options } from 'nodemailer/lib/smtp-transport';
import { getOrganizationGroupAllocationTemplate } from './templates/organization-group-allocation.template';
import { getSystemAlertTemplate } from './templates/system-alert.template';

@Injectable()
export class MailService {
  private transporter: nodemailer.Transporter<SentMessageInfo, Options>;

  constructor(private configService: ConfigService) {
    this.transporter = nodemailer.createTransport({
      host: this.configService.get<string>('mail.host'),
      port: this.configService.get<number>('mail.port'),
      secure: process.env.NODE_ENV?.trim() === 'production',
      auth: {
        user: this.configService.get<string>('mail.user'),
        pass: this.configService.get<string>('mail.pass'),
      },
    });
  }

  async sendOTPEmail(
    email: string,
    otp: string,
    userName: string,
  ): Promise<void> {
    const mailOptions = {
      from: this.configService.get('mail.host'),
      to: email,
      subject: 'Password Reset OTP',
      html: getOTPEmailTemplate(otp, 10, userName),
    };

    await this.transporter.sendMail(mailOptions);
  }

  async sendStaffCredentials(
    email: string,
    username: string,
    password: string,
    organizationName: string,
    orgId: number,
  ): Promise<void> {
    const loginUrl = this.configService.get('LOGIN_URL');

    const formattedOrganizationName = organizationName
      .trim()
      .toLowerCase()
      .replace(/\s+/g, '-');

    const mailOptions = {
      from: this.configService.get('mail.host'),
      to: email,
      subject: 'Your Admin Account Credentials',
      html: getStaffCredentialsTemplate(
        username,
        password,
        loginUrl,
        formattedOrganizationName,
        orgId,
      ),
    };

    await this.transporter.sendMail(mailOptions);
  }

  async sendMemberNotification(
    email: string,
    memberName: string,
    organizationName: string,
    groupName: string,
    chatAppName: string,
    phoneNumber: string,
  ): Promise<void> {
    const mailOptions = {
      from: this.configService.get('mail.host'),
      to: email,
      subject: "You've Been Added to a Group!",
      html: getOrganizationGroupAllocationTemplate(
        memberName,
        organizationName,
        groupName,
        chatAppName,
        phoneNumber,
      ),
    };

    await this.transporter.sendMail(mailOptions);
  }

  async sendSystemAlert(subject: string, message: string): Promise<void> {
    const adminEmail = this.configService.get<string>('ADMIN_EMAIL');
    const fromEmail = this.configService.get<string>('mail.host');

    const mailOptions = {
      from: fromEmail,
      to: adminEmail,
      subject: subject,
      html: getSystemAlertTemplate(subject, message),
    };

    await this.transporter.sendMail(mailOptions);
  }
}
