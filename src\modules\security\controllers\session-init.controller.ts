import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SessionEstablishmentService } from '../services/session-establishment.service';
import { KeyExchangeService } from '../services/key-exchange.service';

/**
 * Session Init Controller
 *
 * Handles HTTP requests related to session initialization,
 * key exchange, and secure session establishment for P2P messaging.
 */
@ApiTags('p2p-sessions')
@Controller('p2p/sessions')
// @UseGuards(JwtAuthGuard)
export class SessionInitController {
  private readonly logger = new Logger(SessionInitController.name);

  constructor(
    private readonly sessionEstablishmentService: SessionEstablishmentService,
    private readonly keyExchangeService: KeyExchangeService,
  ) {}

  /**
   * Initiate a new P2P session
   */
  @Post('initiate')
  @ApiOperation({ summary: 'Initiate a new P2P session' })
  @ApiResponse({ status: 201, description: 'Session initiated successfully' })
  async initiateSession(@Body() sessionData: any) {
    this.logger.log('Initiating new P2P session');
    const { initiatorUserId, recipientUserId } = sessionData;
    return this.sessionEstablishmentService.initiateSession(
      initiatorUserId,
      recipientUserId,
    );
  }

  /**
   * Accept a session invitation
   */
  @Post(':sessionId/accept')
  @ApiOperation({ summary: 'Accept a session invitation' })
  @ApiResponse({ status: 200, description: 'Session accepted successfully' })
  async acceptSession(
    @Param('sessionId') sessionId: string,
    @Body() userData: any,
  ) {
    this.logger.log(`Accepting session ${sessionId}`);
    const { userId } = userData;
    return this.sessionEstablishmentService.acceptSession(sessionId, userId);
  }

  /**
   * Reject a session invitation
   */
  @Post(':sessionId/reject')
  @ApiOperation({ summary: 'Reject a session invitation' })
  @ApiResponse({ status: 200, description: 'Session rejected successfully' })
  async rejectSession(
    @Param('sessionId') sessionId: string,
    @Body() userData: any,
  ) {
    this.logger.log(`Rejecting session ${sessionId}`);
    const { userId } = userData;
    return this.sessionEstablishmentService.rejectSession(sessionId, userId);
  }

  /**
   * Terminate an active session
   */
  @Post(':sessionId/terminate')
  @ApiOperation({ summary: 'Terminate an active session' })
  @ApiResponse({ status: 200, description: 'Session terminated successfully' })
  async terminateSession(
    @Param('sessionId') sessionId: string,
    @Body() userData: any,
  ) {
    this.logger.log(`Terminating session ${sessionId}`);
    const { userId } = userData;
    return this.sessionEstablishmentService.terminateSession(sessionId, userId);
  }

  /**
   * Get active sessions for user
   */
  @Get('active')
  @ApiOperation({ summary: 'Get active sessions for user' })
  @ApiResponse({
    status: 200,
    description: 'Active sessions retrieved successfully',
  })
  async getActiveSessions(@Query('userId') userId: number) {
    this.logger.log(`Getting active sessions for user ${userId}`);
    return this.sessionEstablishmentService.getActiveSessions(userId);
  }

  /**
   * Get public key bundle for user
   */
  @Get('keys/:userId')
  @ApiOperation({ summary: 'Get public key bundle for user' })
  @ApiResponse({
    status: 200,
    description: 'Key bundle retrieved successfully',
  })
  async getPublicKeyBundle(@Param('userId') userId: number) {
    this.logger.log(`Getting public key bundle for user ${userId}`);
    return this.keyExchangeService.getPublicKeyBundle(userId);
  }

  /**
   * Refresh session keys
   */
  @Post(':sessionId/refresh-keys')
  @ApiOperation({ summary: 'Refresh session keys' })
  @ApiResponse({
    status: 200,
    description: 'Session keys refreshed successfully',
  })
  async refreshSessionKeys(@Param('sessionId') sessionId: string) {
    this.logger.log(`Refreshing keys for session ${sessionId}`);
    return this.sessionEstablishmentService.refreshSessionKeys(sessionId);
  }
}
