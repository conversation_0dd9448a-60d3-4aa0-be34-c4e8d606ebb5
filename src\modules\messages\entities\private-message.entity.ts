import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { OrgMember } from '../../members/entities/org-member.entity';
import { MediaFile } from '../../media/entities/media-file.entity';

@Entity('private_messages')
@Index(['chatId', 'seq'])
@Index(['senderId', 'receiverId'])
@Index(['sentAt'])
export class PrivateMessage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'chat_id' })
  @Index()
  chatId: string; // Format: "senderId_receiverId" (smaller_id first)

  @Column({ name: 'sender_id' })
  senderId: number;

  @Column({ name: 'receiver_id' })
  receiverId: number;

  @Column({ name: 'seq', type: 'bigint' })
  seq: number;

  @Column({ name: 'encrypted_content', type: 'text' })
  encryptedContent: string;

  @Column({ name: 'nonce' })
  nonce: string;

  @Column({ name: 'encrypted_metadata', type: 'json', nullable: true })
  encryptedMetaData?: any;

  @Column({ name: 'message_type', default: 'text' })
  messageType: string;

  @Column({ name: 'reply_to_message_id', nullable: true })
  replyToMessageId?: number;

  @Column({ name: 'sent_at', type: 'timestamp' })
  sentAt: Date;

  @Column({ name: 'delivered_count', default: 0 })
  deliveredCount: number;

  @Column({ name: 'read_by_receiver', default: false })
  readByReceiver: boolean;

  @Column({ name: 'read_at', type: 'timestamp', nullable: true })
  readAt?: Date;

  @Column({ name: 'deleted_by_sender', default: false })
  deletedBySender: boolean;

  @Column({ name: 'deleted_by_receiver', default: false })
  deletedByReceiver: boolean;

  @Column({ name: 'deleted_for_everyone', default: false })
  deletedForEveryone: boolean;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => OrgMember, { eager: true })
  @JoinColumn({ name: 'sender_id' })
  sender: OrgMember;

  @ManyToOne(() => OrgMember, { eager: true })
  @JoinColumn({ name: 'receiver_id' })
  receiver: OrgMember;

  @ManyToOne(() => PrivateMessage, { nullable: true })
  @JoinColumn({ name: 'reply_to_message_id' })
  replyTo?: PrivateMessage;
}
