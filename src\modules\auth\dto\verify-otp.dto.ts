import { IsString, IsNotEmpty, Length, IsEnum } from 'class-validator';
import { DeviceType } from '../../members/entities/member-fcm-token.entity';
import { ApiProperty } from '@nestjs/swagger';

export class VerifyOtpDto {
  @ApiProperty({ description: 'Phone number' })
  @IsString()
  @IsNotEmpty()
  phoneNumber: string;

  @ApiProperty({ description: '6-digit OTP code' })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6, { message: 'OTP must be 6 digits' })
  otpCode: string;

  @ApiProperty({ description: 'Device ID' })
  @IsString()
  @IsNotEmpty({ message: 'Device ID is required' })
  deviceId: string;

  @ApiProperty({ description: 'Device type', enum: DeviceType })
  @IsEnum(DeviceType)
  @IsNotEmpty()
  deviceType: DeviceType;

  @ApiProperty({ description: 'FCM token' })
  @IsString()
  @IsNotEmpty()
  fcmToken: string;
}
