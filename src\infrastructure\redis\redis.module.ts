import { Module, DynamicModule, Global, Logger } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import Redis from 'ioredis';
import {
  REDIS_CLIENT,
  REDIS_PUBLISHER,
  REDIS_SUBSCRIBER,
} from './constants/redis.constants';
import { RedisService } from './services/redis.service';
import { PubSubService } from './services/pubsub.service';
import { CacheService } from './services/cache.service';
import { SessionService } from './services/session.service';
import { EventModule } from '../events/event.module';

@Global()
@Module({})
export class RedisModule {
  static forRoot(): DynamicModule {
    return {
      module: RedisModule,
      imports: [ConfigModule, EventModule],
      providers: [
        {
          provide: REDIS_CLIENT,
          useFactory: (configService: ConfigService) => {
            const logger = new Logger('RedisClient');
            const client = new Redis({
              host: configService.get('redis.host'),
              port: configService.get('redis.port'),
              password: configService.get('redis.password') || undefined,
              retryStrategy: (times) => {
                const delay = Math.min(times * 1000, 10000);
                logger.warn(`Retrying Redis connection (attempt ${times})...`);
                return delay;
              },
            });

            client.on('connect', () => logger.log('Redis CLIENT connected'));
            client.on('ready', () => logger.log('Redis CLIENT ready'));
            client.on('error', (err) =>
              logger.error(`Redis CLIENT error: ${err.message}`),
            );
            client.on('close', () =>
              logger.warn('Redis CLIENT connection closed'),
            );
            client.on('reconnecting', () =>
              logger.warn('Redis CLIENT reconnecting...'),
            );

            return client;
          },
          inject: [ConfigService],
        },
        {
          provide: REDIS_PUBLISHER,
          useFactory: (configService: ConfigService) => {
            const logger = new Logger('RedisPublisher');
            const publisher = new Redis({
              host: configService.get('redis.host'),
              port: configService.get('redis.port'),
              password: configService.get('redis.password') || undefined,
            });

            publisher.on('connect', () =>
              logger.log('Redis PUBLISHER connected'),
            );
            publisher.on('ready', () => logger.log('Redis PUBLISHER ready'));
            publisher.on('error', (err) =>
              logger.error(`Redis PUBLISHER error: ${err.message}`),
            );
            publisher.on('close', () =>
              logger.warn('Redis PUBLISHER connection closed'),
            );
            publisher.on('reconnecting', () =>
              logger.warn('Redis PUBLISHER reconnecting...'),
            );

            return publisher;
          },
          inject: [ConfigService],
        },
        {
          provide: REDIS_SUBSCRIBER,
          useFactory: (configService: ConfigService) => {
            const logger = new Logger('RedisSubscriber');
            const subscriber = new Redis({
              host: configService.get('redis.host'),
              port: configService.get('redis.port'),
              password: configService.get('redis.password') || undefined,
            });

            subscriber.on('connect', () =>
              logger.log('Redis SUBSCRIBER connected'),
            );
            subscriber.on('ready', () => logger.log('Redis SUBSCRIBER ready'));
            subscriber.on('error', (err) =>
              logger.error(`Redis SUBSCRIBER error: ${err.message}`),
            );
            subscriber.on('close', () =>
              logger.warn('Redis SUBSCRIBER connection closed'),
            );
            subscriber.on('reconnecting', () =>
              logger.warn('Redis SUBSCRIBER reconnecting...'),
            );

            return subscriber;
          },
          inject: [ConfigService],
        },
        RedisService,
        PubSubService,
        CacheService,
        SessionService,
      ],
      exports: [
        RedisService,
        PubSubService,
        CacheService,
        SessionService,
        REDIS_CLIENT,
        REDIS_PUBLISHER,
        REDIS_SUBSCRIBER,
      ],
    };
  }
}
