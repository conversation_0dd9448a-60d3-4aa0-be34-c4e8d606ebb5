import { IsNotEmpty, IsString, IsOptional, IsObject } from 'class-validator';
import { PRIORITY } from '../constants/notification.constants';

export class SendNotificationDto {
  @IsNotEmpty()
  @IsString({ each: true })
  tokens: string[];

  @IsOptional()
  @IsString()
  title?: string;

  @IsOptional()
  @IsString()
  body?: string;

  @IsOptional()
  @IsObject()
  data?: Record<string, any>;

  @IsOptional()
  @IsString()
  imageUrl?: string;

  @IsOptional()
  @IsString()
  priority?: PRIORITY.HIGH | PRIORITY.NORMAL;
}
