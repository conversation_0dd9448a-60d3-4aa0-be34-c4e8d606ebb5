import { Module } from '@nestjs/common';
import { EventMetricsService } from './services/event-metrics.service';
import { HealthCheckService } from './services/health-check.service';
import { EventStoreModule } from '../../modules/event-store/event-store.module';
import { PrometheusModule } from '@willsoto/nestjs-prometheus';
import { MonitoringController } from './controllers/monitoring.controller';

@Module({
  imports: [EventStoreModule, PrometheusModule.register()],
  controllers: [MonitoringController],
  providers: [EventMetricsService, HealthCheckService],
  exports: [EventMetricsService, HealthCheckService],
})
export class MonitoringModule {}
