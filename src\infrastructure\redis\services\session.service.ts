import { Injectable, Inject, Logger } from '@nestjs/common';
import { REDIS_CLIENT } from '../constants/redis.constants';
import {
  REDIS_KEY_PREFIX,
  REFRESH_TOKEN_KEY,
} from '../constants/redis.constants';
import { RedisClient } from '../interfaces/redis-client.interface';
import { UserType } from '../../../common/types/user-type';
import { Session } from '../interfaces/redis.types';

@Injectable()
export class SessionService {
  private readonly logger = new Logger(SessionService.name);

  constructor(
    @Inject(REDIS_CLIENT) private readonly redisClient: RedisClient,
  ) {}

  /**
   * Generate consistent Redis key for refresh tokens
   */
  private getRefreshTokenKey(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): string {
    return `${REDIS_KEY_PREFIX}:${REFRESH_TOKEN_KEY}:${userType}:${userId}:${tokenId}`;
  }

  /**
   * Generate meta key for refresh token metadata
   */
  private getMetaKey(key: string): string {
    return `${key}:meta`;
  }

  /**
   * Generate pattern for user's refresh tokens
   */
  private getUserTokenPattern(userType: UserType, userId: number): string {
    return `${REDIS_KEY_PREFIX}:${REFRESH_TOKEN_KEY}:${userType}:${userId}:*`;
  }

  /**
   * Get all active sessions for a user with improved performance
   */
  async getUserSessions(
    userType: UserType,
    userId: number,
  ): Promise<Session[]> {
    try {
      const pattern = this.getUserTokenPattern(userType, userId);
      const keys = await this.redisClient.keys(pattern);

      // Filter out meta keys and get token keys only
      const tokenKeys = keys.filter((key) => !key.endsWith(':meta'));

      if (tokenKeys.length === 0) {
        return [];
      }

      // Batch fetch all metadata using pipeline
      const pipeline = this.redisClient.pipeline();
      tokenKeys.forEach((key) => {
        const metaKey = this.getMetaKey(key);
        pipeline.hgetall(metaKey);
      });

      const results = await pipeline.exec();
      const sessions: Session[] = [];

      tokenKeys.forEach((key, index) => {
        const tokenId = key.split(':').pop();
        if (!tokenId) return;

        const metaResult = results?.[index];
        if (metaResult && metaResult[0] === null) {
          // No error
          const meta = metaResult[1] as Record<string, string>;
          if (meta && Object.keys(meta).length > 0) {
            sessions.push({
              tokenId,
              deviceFingerprint: meta.deviceFingerprint,
              userAgent: meta?.userAgent || '',
              deviceName: meta?.deviceName || '',
              createdAt: meta.createdAt || '',
              ip: meta.ip || '',
            });
          }
        }
      });

      return sessions;
    } catch (error) {
      this.logger.error(
        `Failed to get user sessions for user ${userId}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Logout all other sessions except the current one
   */
  async logoutAllOtherSessions(
    userType: UserType,
    userId: number,
    currentTokenId: string,
  ): Promise<void> {
    try {
      const pattern = this.getUserTokenPattern(userType, userId);
      const keys = await this.redisClient.keys(pattern);

      const keysToDelete = keys.filter((key) => {
        if (key.endsWith(':meta')) return false;
        const tokenId = key.split(':').pop();
        return tokenId !== currentTokenId;
      });

      if (keysToDelete.length > 0) {
        const metaKeysToDelete = keysToDelete.map((key) =>
          this.getMetaKey(key),
        );
        const allKeysToDelete = [...keysToDelete, ...metaKeysToDelete];

        // Use pipeline for batch deletion
        const pipeline = this.redisClient.pipeline();
        allKeysToDelete.forEach((key) => pipeline.del(key));
        await pipeline.exec();

        this.logger.debug(
          `Logged out ${keysToDelete.length} other sessions for user ${userId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to logout other sessions for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Logout a specific session
   */
  async logoutSpecificSession(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): Promise<void> {
    try {
      const key = this.getRefreshTokenKey(userType, userId, tokenId);
      const metaKey = this.getMetaKey(key);

      const deletedCount = await this.redisClient.del(key, metaKey);

      if (deletedCount > 0) {
        this.logger.debug(`Logged out session ${tokenId} for user ${userId}`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to logout session ${tokenId} for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get count of active sessions for a user
   */
  async getUserSessionCount(
    userType: UserType,
    userId: number,
  ): Promise<number> {
    try {
      const pattern = this.getUserTokenPattern(userType, userId);
      const keys = await this.redisClient.keys(pattern);
      return keys.filter((key) => !key.endsWith(':meta')).length;
    } catch (error) {
      this.logger.error(
        `Failed to get session count for user ${userId}:`,
        error,
      );
      return 0;
    }
  }
}
