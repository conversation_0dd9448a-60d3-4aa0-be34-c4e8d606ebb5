// handlers/system.handler.ts
import { Injectable, Logger } from '@nestjs/common';
import { Socket, Server } from 'socket.io';
import { PubSubService } from '../../redis/services/pubsub.service';

@Injectable()
export class SystemHandler {
  private readonly logger = new Logger(SystemHandler.name);

  constructor(private readonly pubSubService: PubSubService) {}

  async handleHealthCheck(client: Socket, server: Server) {
    try {
      const connectedClients = await this.getConnectedClientsCount(server);
      const onlineMembers = await this.pubSubService.getOnlineMembers();

      client.emit('health-status', {
        status: 'healthy',
        connectedClients,
        onlineMembersCount: onlineMembers.length,
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
      });
    } catch (error) {
      this.logger.error('Error in health check:', error);
      client.emit('health-status', {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    }
  }

  async handleDebugPresence(client: Socket, data: { memberId: number }) {
    try {
      // Only allow in development environment
      if (process.env.NODE_ENV?.trim() === 'production') {
        client.emit('error', {
          code: 'NOT_ALLOWED',
          message: 'Debug endpoints not available in production',
        });
        return;
      }

      if (!data.memberId || typeof data.memberId !== 'number') {
        client.emit('error', {
          code: 'INVALID_MEMBER_ID',
          message: 'Valid member ID is required',
        });
        return;
      }

      const debugInfo = await this.pubSubService.getPresenceDebugInfo(
        data.memberId,
      );
      client.emit('debug-info', {
        memberId: data.memberId,
        ...debugInfo,
      });
    } catch (error) {
      this.logger.error('Error getting debug info:', error);
      client.emit('error', {
        code: 'DEBUG_ERROR',
        message: 'Failed to get debug information',
      });
    }
  }

  private async getConnectedClientsCount(server: Server): Promise<number> {
    const sockets = await server.fetchSockets();
    return sockets.length;
  }
}
