import {
  IsBoolean,
  IsNotEmpty,
  Is<PERSON><PERSON>ber,
  <PERSON><PERSON><PERSON>al,
  IsString,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateGroupDto {
  @ApiProperty({ description: 'Group name' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiPropertyOptional({ description: 'Group description' })
  @IsOptional()
  @IsString()
  description: string;

  @ApiPropertyOptional({ description: 'URL of group image/file' })
  @IsOptional()
  @IsString()
  fileUrl: string;
}
