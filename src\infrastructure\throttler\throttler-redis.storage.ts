import { Injectable } from '@nestjs/common';
import { ThrottlerStorage } from '@nestjs/throttler';
import { RedisService } from '../redis/services/redis.service';

export interface ThrottlerStorageRecord {
  totalHits: number;
  timeToExpire: number;
  isBlocked: boolean;
  timeToBlockExpire: number;
}

@Injectable()
export class ThrottlerRedisStorage implements ThrottlerStorage {
  constructor(private readonly redisService: RedisService) {}

  async increment(
    key: string,
    ttl: number,
    limit: number,
    blockDuration: number,
    throttlerName: string,
  ): Promise<ThrottlerStorageRecord> {
    const result = await this.redisService.throttlerIncrement(
      key,
      ttl,
      limit,
      blockDuration,
      throttlerName,
    );

    return {
      totalHits: result.totalHits,
      timeToExpire: result.timeToExpire,
      isBlocked: result.isBlocked ?? false,
      timeToBlockExpire: result.timeToBlockExpire ?? 0,
    };
  }
}
