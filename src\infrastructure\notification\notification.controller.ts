import { Body, Controller, Logger, Post } from '@nestjs/common';
import { NotificationService } from './services/notification.service';
import { AckDto } from './dto/acknowledgment.dto';

@Controller('notification')
export class NotificationController {
  private readonly logger = new Logger(NotificationController.name);

  constructor(private readonly notificationService: NotificationService) {}

  @Post('acknowledge')
  async handleAcknowledgement(
    @Body() ackDto: AckDto,
  ): Promise<{ success: boolean }> {
    try {
      this.logger.debug(
        `Received ACK for ${ackDto.eventName} from member ${ackDto.memberId}`,
      );

      this.notificationService.handleAcknowledgement(
        ackDto.memberId,
        ackDto.eventName,
        ackDto.eventTimestamp,
      );

      return { success: true };
    } catch (error) {
      this.logger.error(
        `Failed to process acknowledgement from ${ackDto.memberId}`,
        error.stack,
      );
      return { success: false };
    }
  }
}
