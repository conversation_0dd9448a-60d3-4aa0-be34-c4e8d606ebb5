import {
  RabbitMQModule,
  MessageHandlerErrorBehavior,
} from '@golevelup/nestjs-rabbitmq';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RabbitMQMonitorService } from './rabbitMq.service';
import { OtpConsumer, OtpDlqConsumer } from './consumers';
import { OtpProducer } from './producers';
import {
  MESSAGE_DLQ_EXCHANGE,
  MESSAGE_EXCHANGE,
  OTP_DLQ_EXCHANGE,
  OTP_EXCHANGE,
} from '../../common/constants';
import { RabbitMQExceptionFilter } from './filters/rabbitmq-exception.filter';
import { APP_FILTER } from '@nestjs/core';
import { CoreModule } from '../../core/core.module';
import { MonitoringModule } from '../monitoring/monitoring.module';
import { CircuitBreakerModule } from '../circuit-breaker/circuit-breaker.module';
import { MessageProducer } from './producers/message.producer';
import { EventModule } from '../events/event.module';
import { MessageConsumer } from './consumers/message.consumer';
import { NotificationModule } from '../notification/notification.module';
import { MessageDlqConsumer } from './consumers/message.dlq.consumer';

@Module({
  imports: [
    ConfigModule,
    CoreModule,
    MonitoringModule,
    CircuitBreakerModule,
    EventModule,
    NotificationModule,
    RabbitMQModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => {
        const uri = configService.get<string>('rabbitmq.url');

        if (!uri) {
          throw new Error('RabbitMQ URI is missing in configuration');
        }

        return {
          exchanges: [
            {
              name: OTP_EXCHANGE,
              type: 'direct',
            },
            {
              name: MESSAGE_EXCHANGE,
              type: 'direct',
            },
            {
              name: OTP_DLQ_EXCHANGE,
              type: 'direct',
            },
            {
              name: MESSAGE_DLQ_EXCHANGE,
              type: 'direct',
            },
          ],
          uri,
          isGlobal: true,
          connectionInitOptions: {
            wait: false,
            timeout: 30000,
            retries: -1,
            retryDelay: 5000,
          },
          connectionOptions: {
            heartbeat: 60,
            reconnectTimeInSeconds: 5,
          },
          defaultSubscribeErrorBehavior: MessageHandlerErrorBehavior.ACK,
          defaultRpcErrorBehavior: MessageHandlerErrorBehavior.ACK,
        };
      },
    }),
  ],
  providers: [
    RabbitMQMonitorService,
    OtpProducer,
    OtpConsumer,
    OtpDlqConsumer,
    MessageProducer,
    MessageConsumer,
    MessageDlqConsumer,
    {
      provide: APP_FILTER,
      useClass: RabbitMQExceptionFilter,
    },
  ],
  exports: [OtpProducer, MessageProducer],
})
export class RabbitMQConfigModule {}
