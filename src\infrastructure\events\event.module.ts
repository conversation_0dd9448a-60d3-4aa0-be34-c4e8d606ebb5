import { Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { EventBusService } from './event-bus.service';
import { EventStoreModule } from '../../modules/event-store/event-store.module';
import { EventMetricsService } from '../monitoring/services/event-metrics.service';

@Module({
  imports: [EventStoreModule],
  providers: [EventBusService, EventMetricsService],
  exports: [EventBusService, EventMetricsService],
})
export class EventModule {}
