import { Injectable } from '@nestjs/common';
import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { OTP_EXCHANGE, OTP_SEND_ROUTING_KEY } from '../../../common/constants';

@Injectable()
export class OtpProducer {
  constructor(private readonly amqpConnection: AmqpConnection) {}

  async sendOtpTask(payload: { phoneNumber: string; otp: string }) {
    const message = {
      ...payload,
      createdAt: new Date().toISOString(),
    };

    await this.amqpConnection.publish(
      OTP_EXCHANGE,
      OTP_SEND_ROUTING_KEY,
      message,
    );
  }
}
