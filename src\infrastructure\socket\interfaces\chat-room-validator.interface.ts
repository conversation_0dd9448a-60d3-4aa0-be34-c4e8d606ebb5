import { RoomType } from '../services/room-manager.service';

export interface IChatRoomValidator {
  validateMemberInGroup(memberId: number, groupId: number): Promise<boolean>;
  validateMemberInPrivateChat(
    memberId: number,
    otherMemberId: number,
  ): Promise<boolean>;
  validateMemberInOrganization(
    memberId: number,
    organizationId: number,
  ): Promise<boolean>;
  getMemberActiveRooms(memberId: number): Promise<string[]>;
  getClientsInRoom(
    roomType: RoomType,
    ...identifiers: (string | number)[]
  ): Promise<number[]>;
}
