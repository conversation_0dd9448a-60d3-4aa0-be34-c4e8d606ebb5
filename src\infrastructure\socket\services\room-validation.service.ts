import { Injectable, Logger } from '@nestjs/common';
import { IChatRoomValidator } from '../interfaces/chat-room-validator.interface';
import { Server } from 'socket.io';
import { RoomManagerService, RoomType } from './room-manager.service';

@Injectable()
export class RoomValidationService implements IChatRoomValidator {
  private readonly logger = new Logger(RoomValidationService.name);
  private server: Server;

  constructor(private readonly roomManager: RoomManagerService) {}

  // This method should be called from SocketGateway after server initialization
  setServer(server: Server): void {
    this.server = server;
  }

  /**
   * Validate if a member is currently connected and joined to a specific group room
   */
  async validateMemberInGroup(
    memberId: number,
    groupId: number,
  ): Promise<boolean> {
    try {
      if (!this.server) {
        this.logger.warn('Server not initialized');
        return false;
      }

      const groupRoomName = this.roomManager.generateRoomName(
        RoomType.GROUP,
        groupId,
      );
      const memberRoomName = this.roomManager.generateRoomName(
        RoomType.MEMBER,
        memberId,
      );

      // Get all sockets in the group room
      const groupSockets = await this.server.in(groupRoomName).fetchSockets();

      // Check if any socket in the group room belongs to the member
      const memberInGroup = groupSockets.some(
        (socket) => socket.data.memberId === memberId,
      );

      // Additional check: verify the member is online (has active connection)
      const memberSockets = await this.server.in(memberRoomName).fetchSockets();
      const memberIsOnline = memberSockets.length > 0;

      const isValid = memberInGroup && memberIsOnline;

      this.logger.debug(
        `Member ${memberId} in group ${groupId}: ${isValid} (inGroup: ${memberInGroup}, online: ${memberIsOnline})`,
      );

      return isValid;
    } catch (error) {
      this.logger.error(
        `Error validating member ${memberId} in group ${groupId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Validate if a member is currently connected and in a private chat with another member
   */
  async validateMemberInPrivateChat(
    memberId: number,
    otherMemberId: number,
  ): Promise<boolean> {
    try {
      if (!this.server) {
        this.logger.warn('Server not initialized');
        return false;
      }

      const privateRoomName = this.roomManager.generateRoomName(
        RoomType.PRIVATE,
        memberId,
        otherMemberId,
      );
      const memberRoomName = this.roomManager.generateRoomName(
        RoomType.MEMBER,
        memberId,
      );

      // Get all sockets in the private chat room
      const privateSockets = await this.server
        .in(privateRoomName)
        .fetchSockets();

      // Check if the member is in the private chat room
      const memberInPrivateChat = privateSockets.some(
        (socket) => socket.data.memberId === memberId,
      );

      // Verify the member is online
      const memberSockets = await this.server.in(memberRoomName).fetchSockets();
      const memberIsOnline = memberSockets.length > 0;

      const isValid = memberInPrivateChat && memberIsOnline;

      this.logger.debug(
        `Member ${memberId} in private chat with ${otherMemberId}: ${isValid} (inChat: ${memberInPrivateChat}, online: ${memberIsOnline})`,
      );

      return isValid;
    } catch (error) {
      this.logger.error(
        `Error validating member ${memberId} in private chat with ${otherMemberId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Validate if a member is currently connected and in an organization room
   */
  async validateMemberInOrganization(
    memberId: number,
    organizationId: number,
  ): Promise<boolean> {
    try {
      if (!this.server) {
        this.logger.warn('Server not initialized');
        return false;
      }

      const organizationRoomName = this.roomManager.generateRoomName(
        RoomType.ORGANIZATION,
        organizationId,
      );
      const memberRoomName = this.roomManager.generateRoomName(
        RoomType.MEMBER,
        memberId,
      );

      // Get all sockets in the organization room
      const organizationSockets = await this.server
        .in(organizationRoomName)
        .fetchSockets();

      // Check if the member is in the organization room
      const memberInOrganization = organizationSockets.some(
        (socket) => socket.data.memberId === memberId,
      );

      // Verify the member is online
      const memberSockets = await this.server.in(memberRoomName).fetchSockets();
      const memberIsOnline = memberSockets.length > 0;

      const isValid = memberInOrganization && memberIsOnline;

      this.logger.debug(
        `Member ${memberId} in organization ${organizationId}: ${isValid} (inOrg: ${memberInOrganization}, online: ${memberIsOnline})`,
      );

      return isValid;
    } catch (error) {
      this.logger.error(
        `Error validating member ${memberId} in organization ${organizationId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * Get all active rooms a member is currently in
   */
  async getMemberActiveRooms(memberId: number): Promise<string[]> {
    try {
      if (!this.server) {
        this.logger.warn('Server not initialized');
        return [];
      }

      const memberRoomName = this.roomManager.generateRoomName(
        RoomType.MEMBER,
        memberId,
      );

      // Get all member's sockets
      const memberSockets = await this.server.in(memberRoomName).fetchSockets();

      if (memberSockets.length === 0) {
        return [];
      }

      // Collect all unique rooms from all member's sockets
      const allRooms = new Set<string>();
      memberSockets.forEach((socket) => {
        socket.rooms.forEach((room) => {
          // Exclude the socket's own room ID and member room
          if (room !== socket.id && room !== memberRoomName) {
            allRooms.add(room);
          }
        });
      });

      const rooms = Array.from(allRooms);
      this.logger.debug(`Member ${memberId} active rooms:`, rooms);

      return rooms;
    } catch (error) {
      this.logger.error(
        `Error getting active rooms for member ${memberId}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Get all member IDs currently in a specific room
   */
  async getClientsInRoom(
    roomType: RoomType,
    ...identifiers: (string | number)[]
  ): Promise<number[]> {
    try {
      if (!this.server) {
        this.logger.warn('Server not initialized');
        return [];
      }

      const roomName = this.roomManager.generateRoomName(
        roomType,
        ...identifiers,
      );

      const sockets = await this.server.in(roomName).fetchSockets();
      const memberIds = sockets
        .map((socket) => socket.data.memberId)
        .filter((memberId) => memberId !== undefined)
        .filter((memberId, index, arr) => arr.indexOf(memberId) === index); // Remove duplicates

      this.logger.debug(`Clients in ${roomType} room ${roomName}:`, memberIds);

      return memberIds;
    } catch (error) {
      this.logger.error(`Error getting clients in ${roomType} room:`, error);
      return [];
    }
  }

  /**
   * Validate if both members are in the same private chat room
   */
  async validateBothMembersInPrivateChat(
    member1Id: number,
    member2Id: number,
  ): Promise<boolean> {
    try {
      const member1Valid = await this.validateMemberInPrivateChat(
        member1Id,
        member2Id,
      );
      const member2Valid = await this.validateMemberInPrivateChat(
        member2Id,
        member1Id,
      );

      return member1Valid && member2Valid;
    } catch (error) {
      this.logger.error(
        `Error validating both members in private chat:`,
        error,
      );
      return false;
    }
  }

  /**
   * Get room statistics
   */
  async getRoomValidationStats(): Promise<{
    totalConnections: number;
    roomCounts: Record<string, number>;
  }> {
    try {
      if (!this.server) {
        return { totalConnections: 0, roomCounts: {} };
      }

      const allSockets = await this.server.fetchSockets();
      const totalConnections = allSockets.length;

      const roomCounts: Record<string, number> = {};

      // Count sockets per room type
      for (const socket of allSockets) {
        for (const room of socket.rooms) {
          if (room !== socket.id) {
            // Skip socket's own room
            const roomType = this.extractRoomType(room);
            roomCounts[roomType] = (roomCounts[roomType] || 0) + 1;
          }
        }
      }

      return { totalConnections, roomCounts };
    } catch (error) {
      this.logger.error('Error getting room validation stats:', error);
      return { totalConnections: 0, roomCounts: {} };
    }
  }

  private extractRoomType(roomName: string): string {
    if (roomName.startsWith('member_')) return 'member';
    if (roomName.startsWith('group_')) return 'group';
    if (roomName.startsWith('private_')) return 'private';
    if (roomName.startsWith('organization_')) return 'organization';
    return 'unknown';
  }
}
