import { Catch, ExceptionFilter, ArgumentsHost, Logger } from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { AxiosError } from 'axios';
import { NonRetryableRpcException } from './non-retryable-rpc.exception';

@Catch()
export class RabbitMQExceptionFilter implements ExceptionFilter {
  private readonly logger = new Logger(RabbitMQExceptionFilter.name);

  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToRpc();
    const data = ctx.getData();

    if (exception instanceof AxiosError) {
      return this.handleAxiosError(exception, data);
    }

    if (exception instanceof RpcException) {
      return this.handleRpcException(exception, data);
    }

    return this.handleUnknownError(exception, data);
  }

  private handleAxiosError(error: AxiosError, data: any) {
    const status = error.response?.status;
    const responseData = error.response?.data;

    this.logger.error(`HTTP Error ${status}: ${error.message}`, {
      url: error.config?.url,
      method: error.config?.method,
      responseData,
      data,
      stack: error.stack,
    });

    const responseMessage =
      responseData &&
      typeof responseData === 'object' &&
      'message' in responseData
        ? (responseData as { message?: string }).message
        : undefined;

    if (this.isNonRetryable(status, responseMessage)) {
      this.logger.warn(
        'Discarding message: Non-retryable HTTP error detected.',
      );
      throw new NonRetryableRpcException(
        responseMessage || 'Non-retryable Axios error',
      );
    }

    throw new RpcException({
      message: error.message,
      status: status || 500,
      retryable: true,
    });
  }

  private handleRpcException(error: RpcException, data: any) {
    this.logger.error('RPC Exception thrown', {
      error: error.getError(),
      data,
      stack: error.stack,
    });

    throw error;
  }

  private handleUnknownError(exception: any, data: any) {
    const message = exception?.message || 'Unknown error occurred';
    this.logger.error(
      'Unhandled exception during RabbitMQ message processing',
      {
        message,
        data,
        stack: exception?.stack,
      },
    );

    // By default, treat as non-retryable
    throw new NonRetryableRpcException(message);
  }

  private isNonRetryable(status?: number, message?: string): boolean {
    if (!status) return false;

    const nonRetryableStatuses = [400, 401, 403, 404];
    if (nonRetryableStatuses.includes(status)) {
      return true;
    }

    const lowered = message?.toLowerCase() || '';
    const patterns = [
      'exceeded your sending limit',
      'invalid api key',
      'unauthorized',
      'forbidden',
      'not found',
      'invalid phone number',
      'insufficient credits',
    ];

    return patterns.some((pattern) => lowered.includes(pattern));
  }
}
