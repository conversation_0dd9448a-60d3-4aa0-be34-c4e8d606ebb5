import { AdminUserType, MemberUserType } from 'src/common/types/user-type';

interface BaseJwtPayload {
  sub: number;
  iat?: number;
  exp?: number;
}

export interface JwtAdminPayload extends BaseJwtPayload {
  type: AdminUserType;
  roleId: number;
  orgId?: number;
}

export interface JwtOrgMemberPayload extends BaseJwtPayload {
  type: MemberUserType;
  orgId: number;
}

export type JwtPayload = JwtAdminPayload | JwtOrgMemberPayload;
