import { Controller, Get } from '@nestjs/common';
import {
  HealthCheckService,
  HealthStatus,
} from '../services/health-check.service';
import { Public } from 'src/common/decorators/public.decorator';

@Controller('health')
export class HealthController {
  constructor(private readonly healthCheckService: HealthCheckService) {}

  @Public()
  @Get()
  async checkHealth(): Promise<HealthStatus> {
    return this.healthCheckService.getHealthStatus();
  }
}
