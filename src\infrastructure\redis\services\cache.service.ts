import { Injectable, Inject, Logger } from '@nestjs/common';
import { REDIS_CLIENT } from '../constants/redis.constants';
import { FCM_TOKEN_KEY } from '../constants/redis.constants';
import { RedisClient } from '../interfaces/redis-client.interface';

@Injectable()
export class CacheService {
  private readonly logger = new Logger(CacheService.name);

  constructor(
    @Inject(REDIS_CLIENT) private readonly redisClient: RedisClient,
  ) {}

  /**
   * FCM Token Management
   */

  /**
   * Store FCM token for a member and device
   */
  async storeFcmToken(
    memberId: number,
    token: string,
    deviceId: string,
  ): Promise<void> {
    try {
      const key = `${FCM_TOKEN_KEY}:${memberId}:${deviceId}`;
      await this.redisClient.set(key, token);

      this.logger.debug(
        `Stored FCM token for member ${memberId} on device ${deviceId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to store FCM token for member ${memberId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get all FCM tokens for a member
   */
  async getFcmTokens(memberId: number): Promise<string[]> {
    try {
      const pattern = `${FCM_TOKEN_KEY}:${memberId}:*`;
      const keys = await this.redisClient.keys(pattern);

      if (!keys.length) return [];

      const tokens = await this.redisClient.mget(...keys);
      return tokens.filter(Boolean) as string[];
    } catch (error) {
      this.logger.error(
        `Failed to get FCM tokens for member ${memberId}:`,
        error,
      );
      return [];
    }
  }

  /**
   * Get FCM token for a specific device
   */
  async getFcmToken(
    memberId: number,
    deviceId: string,
  ): Promise<string | null> {
    try {
      const key = `${FCM_TOKEN_KEY}:${memberId}:${deviceId}`;
      return await this.redisClient.get(key);
    } catch (error) {
      this.logger.error(
        `Failed to get FCM token for member ${memberId} device ${deviceId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Remove FCM token for a specific device
   */
  async removeFcmToken(memberId: number, deviceId: string): Promise<void> {
    try {
      const key = `${FCM_TOKEN_KEY}:${memberId}:${deviceId}`;
      const deletedCount = await this.redisClient.del(key);

      if (deletedCount > 0) {
        this.logger.debug(
          `Removed FCM token for member ${memberId} device ${deviceId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to remove FCM token for member ${memberId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove all FCM tokens for a member
   */
  async removeAllFcmTokens(memberId: number): Promise<void> {
    try {
      const pattern = `${FCM_TOKEN_KEY}:${memberId}:*`;
      const keys = await this.redisClient.keys(pattern);

      if (keys.length > 0) {
        const deletedCount = await this.redisClient.del(...keys);

        this.logger.debug(
          `Removed ${deletedCount} FCM tokens for member ${memberId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to remove all FCM tokens for member ${memberId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Get count of FCM tokens for a member
   */
  async getFcmTokenCount(memberId: number): Promise<number> {
    try {
      const pattern = `${FCM_TOKEN_KEY}:${memberId}:*`;
      const keys = await this.redisClient.keys(pattern);
      return keys.length;
    } catch (error) {
      this.logger.error(
        `Failed to get FCM token count for member ${memberId}:`,
        error,
      );
      return 0;
    }
  }

  /**
   * Check if member has any FCM tokens
   */
  async hasFcmTokens(memberId: number): Promise<boolean> {
    try {
      const count = await this.getFcmTokenCount(memberId);
      return count > 0;
    } catch (error) {
      this.logger.error(
        `Failed to check FCM tokens for member ${memberId}:`,
        error,
      );
      return false;
    }
  }

  /**
   * General cache operations
   */

  /**
   * Set a value in cache with optional TTL
   */
  async set(key: string, value: string, ttlSeconds?: number): Promise<void> {
    try {
      if (ttlSeconds) {
        await this.redisClient.set(key, value, 'EX', ttlSeconds);
      } else {
        await this.redisClient.set(key, value);
      }
    } catch (error) {
      this.logger.error(`Failed to set cache key ${key}:`, error);
      throw error;
    }
  }

  /**
   * Get a value from cache
   */
  async get(key: string): Promise<string | null> {
    try {
      return await this.redisClient.get(key);
    } catch (error) {
      this.logger.error(`Failed to get cache key ${key}:`, error);
      return null;
    }
  }

  /**
   * Delete a key from cache
   */
  async del(key: string): Promise<number> {
    try {
      return await this.redisClient.del(key);
    } catch (error) {
      this.logger.error(`Failed to delete cache key ${key}:`, error);
      return 0;
    }
  }

  /**
   * Check if key exists in cache
   */
  async exists(key: string): Promise<boolean> {
    try {
      const result = await this.redisClient.exists(key);
      return result === 1;
    } catch (error) {
      this.logger.error(`Failed to check existence of key ${key}:`, error);
      return false;
    }
  }
}
