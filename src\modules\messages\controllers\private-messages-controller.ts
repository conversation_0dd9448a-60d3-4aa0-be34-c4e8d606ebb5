import {
  Controller,
  Post,
  Body,
  Get,
  Param,
  BadRequestException,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { PrivateMessageService } from '../services/pvt-messages.service';
import { RetryPrivateMessageDto } from '../dto/retry-privatemessage.dto';
import { SendPrivateMessageDto } from '../dto/send-private-message.dto';

@ApiTags('PrivateMessages')
@ApiBearerAuth()
@Controller('private-messages')
export class PrivateMessagesController {
  constructor(private readonly privateMessageService: PrivateMessageService) {}

  @Post()
  async send(@Body() dto: SendPrivateMessageDto) {
    // You may need to generate seq and queryRunner here, adjust as needed
    const seq = await this.privateMessageService.getNextSequenceNumber(
      dto.senderId,
      dto.receiverId,
      dto.queryRunner, // optional if used
    );
    return this.privateMessageService.createPrivateMessage(
      dto.senderId,
      dto.receiverId,
      dto.encryptedContent,
      seq,
      dto.queryRunner,
      dto,
    );
  }
  /** Retry sending a failed private message */
  @Post('retry')
  async retryPrivateMessage(@Body() retryDto: RetryPrivateMessageDto) {
    try {
      // Generate sequence dynamically inside service
      const seq = await this.privateMessageService.getNextSequenceNumber(
        retryDto.sendMessageDto.senderId,
        retryDto.sendMessageDto.receiverId,
        retryDto.sendMessageDto.queryRunner, // optional if used
      );

      const message = await this.privateMessageService.createPrivateMessage(
        retryDto.sendMessageDto.senderId,
        retryDto.sendMessageDto.receiverId,
        retryDto.sendMessageDto.encryptedContent,
        seq,
        retryDto.sendMessageDto.queryRunner,
        {
          ...retryDto.sendMessageDto,
          messageIndex: seq,
        },
      );

      return {
        success: true,
        messageId: message.id,
        message: 'Private message queued for delivery',
      };
    } catch (error) {
      console.error(error);
      throw new BadRequestException('Failed to retry private message');
    }
  }

  /** Fetch the last message between two members */
  @Get('last')
  @ApiOperation({ summary: 'Get the last private message between two members' })
  @ApiResponse({
    status: 200,
    description: 'Last private message fetched successfully',
  })
  async getLastMessage(
    @Query('member1Id') member1Id: number,
    @Query('member2Id') member2Id: number,
  ) {
    try {
      const lastMessage =
        await this.privateMessageService.getLastMessageForThread(
          member1Id,
          member2Id,
        );

      return {
        success: true,
        lastMessage,
      };
    } catch (error) {
      console.error(error);
      throw new BadRequestException('Failed to fetch last private message');
    }
  }
}
