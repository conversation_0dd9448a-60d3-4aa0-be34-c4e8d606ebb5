import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { Request } from 'express';
import {
  JwtPayload,
  JwtAdminPayload,
  JwtOrgMemberPayload,
} from '../interfaces/jwt-payload.interface';
import { RefreshTokenPayload } from '../interfaces/refresh-token-payload.interface';
import { RedisService } from 'src/infrastructure/redis/services/redis.service';
import { UsersService } from 'src/modules/users/services/users.service';
import { MembersService } from 'src/modules/members/services/members.service';
import { USER_TYPE } from 'src/common/constants/user-type.constants';
import {
  ClientDeviceInfo,
  extractClientDeviceInfo,
} from 'src/common/utils/user.device-info';
import { AdminUserType, UserType } from 'src/common/types/user-type';
import { v4 as uuidv4 } from 'uuid';
import * as nacl from 'tweetnacl';
import * as naclUtil from 'tweetnacl-util';

@Injectable()
export class TokenService {
  private readonly logger = new Logger(TokenService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
    private readonly userService: UsersService,
    private readonly memberService: MembersService,
  ) {}

  async refreshToken(
    refreshToken: string,
    request: Request,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      // Extract device info from request
      const deviceInfo: ClientDeviceInfo = extractClientDeviceInfo(request);
      const { deviceName, userAgent, ip } = deviceInfo;

      // Verify the refresh token
      const decoded = await this.decodeRefreshToken(refreshToken);

      // Validate token structure
      this.validateRefreshTokenPayload(decoded);

      const { sub, tokenId, type } = decoded;

      const challenge = request.headers['x-challenge'] as string;
      const signature = request.headers['x-signature'] as string;

      if (!challenge) {
        throw new UnauthorizedException('Challenge is required');
      }

      // 4. Retrieve and validate the challenge from Redis
      const challengeData =
        await this.redisService.getAndValidateChallenge(challenge);

      if (!challengeData) {
        throw new UnauthorizedException('Invalid or expired challenge');
      }

      // 5. Verify the challenge matches the user context
      if (
        challengeData.userId !== sub ||
        challengeData.userType !== type ||
        challengeData.tokenId !== tokenId
      ) {
        throw new UnauthorizedException('Challenge context mismatch');
      }

      // Retrieve token data from Redis
      const tokenData = await this.redisService.getRefreshTokenWithMeta(
        type,
        sub,
        tokenId,
      );

      if (!tokenData || tokenData.token !== refreshToken) {
        throw new UnauthorizedException(
          'Refresh token not found or mismatched',
        );
      }

      // Get current user data and validate
      const currentUserData = await this.getCurrentUserData(sub, type);

      if (!currentUserData) {
        await this.redisService.removeRefreshToken(type, sub, tokenId);
        throw new UnauthorizedException('User not found - please login again');
      }

      // Validate user data hasn't changed significantly
      await this.validateUserDataChanges(
        decoded,
        currentUserData,
        type,
        sub,
        tokenId,
      );

      const publicKey = await this.redisService.getPublicKey(
        type as UserType,
        sub,
        tokenId,
      );

      if (!publicKey) {
        throw new UnauthorizedException(
          'No public key registered for this device',
        );
      }

      const isSignatureValid = await this.verifySignature(
        challenge,
        signature,
        publicKey,
      );

      if (!isSignatureValid) {
        throw new UnauthorizedException('Invalid signature');
      }

      // Build new payload
      const newPayload = this.buildPayloadFromUserData(
        sub,
        type,
        currentUserData,
      );

      // Generate new tokens
      const {
        accessToken,
        refreshToken: newRefreshToken,
        tokenId: newTokenId,
      } = await this.generateTokens(newPayload);

      // Store new token and remove old one
      const refreshTokenExpiresIn = this.getRefreshTokenExpiresInSeconds();

      await Promise.all([
        this.redisService.removeRefreshToken(type, sub, tokenId),
        this.redisService.storeRefreshToken(
          type,
          sub,
          newRefreshToken,
          newTokenId,
          userAgent,
          deviceName,
          ip,
          refreshTokenExpiresIn,
        ),
        this.redisService.migratePublicKey(
          type as UserType,
          sub,
          tokenId,
          newTokenId,
          publicKey,
        ),
      ]);

      this.logger.log(`Token refreshed successfully for user ${sub}`);

      return { accessToken, refreshToken: newRefreshToken };
    } catch (error) {
      this.handleRefreshTokenError(error);
    }
  }

  /**
   * Generate JWT tokens (access and refresh)
   */
  async generateTokens(payload: Omit<JwtPayload, 'iat' | 'exp'>) {
    const tokenId = uuidv4();

    const accessToken = await this.jwtService.signAsync(payload, {
      secret: this.configService.get<string>('jwt.secret'),
      expiresIn: this.configService.get<string>('jwt.expiresIn', '1d'),
    });

    const refreshToken = await this.jwtService.signAsync(
      {
        ...payload,
        tokenId,
      },
      {
        secret: this.configService.get<string>('jwt.refreshSecret'),
        expiresIn: this.configService.get<string>('jwt.refreshExpiresIn', '7d'),
      },
    );

    return {
      accessToken,
      refreshToken,
      tokenId,
    };
  }

  async storeRefreshToken(
    type: UserType,
    userId: number,
    refreshToken: string,
    tokenId: string,
    req: Request,
  ): Promise<void> {
    const { deviceName, userAgent, ip } = extractClientDeviceInfo(req);
    const expiresIn = this.getRefreshTokenExpiresInSeconds();

    await this.redisService.storeRefreshToken(
      type,
      userId,
      refreshToken,
      tokenId,
      userAgent,
      deviceName,
      ip,
      expiresIn,
    );
  }

  async decodeRefreshToken(refreshToken: string): Promise<RefreshTokenPayload> {
    try {
      return await this.jwtService.verifyAsync<RefreshTokenPayload>(
        refreshToken,
        {
          secret: this.configService.get<string>('jwt.refreshSecret'),
        },
      );
    } catch (error) {
      this.logger.error(`Refresh token decode error: ${error.message}`);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async verifySignature(
    message: string,
    signature: string,
    publicKey: string,
  ): Promise<boolean> {
    try {
      const messageBytes = naclUtil.decodeUTF8(message);
      const signatureBytes = naclUtil.decodeBase64(signature);
      const publicKeyBytes = naclUtil.decodeBase64(publicKey);

      // Use detached.verify for simpler signature verification
      return nacl.sign.detached.verify(
        messageBytes,
        signatureBytes,
        publicKeyBytes,
      );
    } catch (error) {
      this.logger.error('Signature verification failed:', error);
      return false;
    }
  }

  // ===== PRIVATE HELPER METHODS =====

  private validateRefreshTokenPayload(decoded: RefreshTokenPayload): void {
    const { sub, tokenId, type } = decoded;

    if (!sub || !tokenId || !type) {
      throw new UnauthorizedException('Invalid refresh token payload');
    }

    const validUserTypes = [
      USER_TYPE.ORG_MEMBER,
      USER_TYPE.ORG_ADMIN,
      USER_TYPE.PRODUCT_ADMIN,
    ];
    if (!validUserTypes.includes(type)) {
      throw new UnauthorizedException('Invalid user type in token');
    }

    // Validate type-specific fields
    if (type === USER_TYPE.ORG_ADMIN || type === USER_TYPE.PRODUCT_ADMIN) {
      if (!decoded.roleId || typeof decoded.roleId !== 'number') {
        throw new UnauthorizedException(
          'Missing or invalid roleId for admin user',
        );
      }
    }

    if (type === USER_TYPE.ORG_ADMIN || type === USER_TYPE.ORG_MEMBER) {
      if (!decoded.orgId || typeof decoded.orgId !== 'number') {
        throw new UnauthorizedException(
          'Missing or invalid orgId for organization user',
        );
      }
    }
  }

  private async getCurrentUserData(
    userId: number,
    userType: UserType,
  ): Promise<{
    roleId?: number;
    orgId?: number;
  } | null> {
    try {
      if (
        userType === USER_TYPE.ORG_ADMIN ||
        userType === USER_TYPE.PRODUCT_ADMIN
      ) {
        const admin = await this.userService.findById(userId);
        return admin ? { roleId: admin.roleId, orgId: admin.orgId } : null;
      } else if (userType === USER_TYPE.ORG_MEMBER) {
        const member = await this.memberService.findOne(userId);
        return member ? { orgId: member.orgId } : null;
      }
      return null;
    } catch (error) {
      this.logger.error(
        `Error fetching user data for ID ${userId}:`,
        error.message,
      );
      return null;
    }
  }

  private async validateUserDataChanges(
    decoded: RefreshTokenPayload,
    currentUserData: any,
    type: UserType,
    userId: number,
    tokenId: string,
  ): Promise<void> {
    if (type === USER_TYPE.ORG_MEMBER) {
      if (currentUserData.orgId !== decoded.orgId) {
        await this.redisService.removeRefreshToken(type, userId, tokenId);
        throw new UnauthorizedException(
          'Organization membership changed - please login again',
        );
      }
    } else if (
      type === USER_TYPE.ORG_ADMIN ||
      type === USER_TYPE.PRODUCT_ADMIN
    ) {
      if ('roleId' in decoded && currentUserData.roleId !== decoded.roleId) {
        await this.redisService.removeRefreshToken(type, userId, tokenId);
        throw new UnauthorizedException('Role changed - please login again');
      }

      if (
        type === USER_TYPE.ORG_ADMIN &&
        currentUserData.orgId !== decoded.orgId
      ) {
        await this.redisService.removeRefreshToken(type, userId, tokenId);
        throw new UnauthorizedException(
          'Organization assignment changed - please login again',
        );
      }
    }
  }

  private buildPayloadFromUserData(
    userId: number,
    type: UserType,
    userData: any,
  ): JwtPayload {
    if (type === USER_TYPE.ORG_MEMBER) {
      return {
        sub: userId,
        type: USER_TYPE.ORG_MEMBER,
        orgId: userData.orgId,
      } as JwtOrgMemberPayload;
    }

    return {
      sub: userId,
      type: type as AdminUserType,
      roleId: userData.roleId,
      ...(type === USER_TYPE.ORG_ADMIN && { orgId: userData.orgId }),
    } as JwtAdminPayload;
  }

  private getRefreshTokenExpiresInSeconds(): number {
    const defaultDays = 7;
    const configValue = this.configService.get<string>(
      'jwt.refreshExpiresIn',
      `${defaultDays}d`,
    );
    const days = parseInt(configValue.replace('d', ''), 10) || defaultDays;
    return days * 24 * 60 * 60;
  }

  private handleRefreshTokenError(error: any): never {
    if (error instanceof UnauthorizedException) {
      this.logger.warn(`Refresh token unauthorized: ${error.message}`);
    } else {
      this.logger.error(`Refresh token error: ${error.message}`, error.stack);
    }
    throw new UnauthorizedException('Could not refresh token');
  }
}
