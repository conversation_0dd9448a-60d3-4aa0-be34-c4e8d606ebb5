import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
} from 'typeorm';
import { Group } from '../../groups/entities/group.entity';
import {
  GroupChangeType,
  GroupLogAction,
} from '../enums/group-log-action.enum';

@Entity('group_change_logs')
export class GroupChangeLog {
  @PrimaryGeneratedColumn()
  id: number;

  // Global sequence number for all changes in this group
  @Column({ type: 'bigint' })
  seq: number;

  @Column({
    name: 'change_type',
    type: 'enum',
    enum: GroupChangeType,
  })
  changeType: GroupChangeType;

  @Column({
    type: 'enum',
    enum: GroupLogAction,
    nullable: true,
  })
  action: GroupLogAction;

  // For member-related changes, null if group-level meta change
  @Column({ name: 'member_id', nullable: true })
  memberId: number;

  // Name of the changed field (e.g. 'name', 'description', 'isMute', etc)
  @Column({ name: 'changed_field', nullable: true })
  changedField: string;

  @Column({ name: 'old_value', type: 'text', nullable: true })
  oldValue: string;

  @Column({ name: 'new_value', type: 'text', nullable: true })
  newValue: string;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ManyToOne(() => Group, (group) => group.changeLogs, { nullable: true })
  @JoinColumn({ name: 'group_id' })
  group: Group;
}
