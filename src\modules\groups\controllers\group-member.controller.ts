import {
  BadRequestException,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  NotFoundException,
  Param,
  ParseIntPipe,
  Query,
  Req,
  Res,
  UnauthorizedException,
  UseInterceptors,
} from '@nestjs/common';
import { GroupMembersService } from '../services/group-member.service';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';
import { Response, Request } from 'express';
import { UsePermanentUrls } from '../../../common/decorators/image.decorator';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';

@ApiTags('Group Members')
@ApiBearerAuth()
@Controller('group-members')
export class GroupMembersController {
  constructor(private readonly groupMembersService: GroupMembersService) {}

  // @Get(':memberId')
  // @HttpCode(HttpStatus.OK)
  // @UsePermanentUrls(['imageUrl'])
  // @UseInterceptors(ImageUrlInterceptor)
  // async getGroupsByMemberId(
  //   @Param('memberId', ParseIntPipe) memberId: number,
  //   @Res({ passthrough: true }) response: Response,
  //   @Req() request: Request,
  // ) {
  //   const user = request.user;
  //   if (user.id !== Number(memberId)) {
  //     throw new UnauthorizedException();
  //   }
  //   const result = await this.groupMembersService.getUserGroups(memberId);
  //   response.locals.message = 'Group member fetched successfully';
  //   return result;
  // }

  @Get(':groupId/delta')
  @UseInterceptors(ImageUrlInterceptor)
  @UsePermanentUrls(['imageUrl'])
  @ApiOperation({ summary: 'Delta sync for a group' })
  @ApiParam({ name: 'groupId', type: Number, description: 'ID of the group' })
  @ApiQuery({
    name: 'seq',
    type: Number,
    description: 'Sequence number for delta sync',
  })
  @ApiResponse({ status: 200, description: 'Delta sync returned successfully' })
  async deltaSyncGroup(
    @Param('groupId', ParseIntPipe) groupId: number,
    @Query('seq', ParseIntPipe) sequence: number,
    @Req() req: Request,
  ) {
    const userId = req.user?.id;
    return this.groupMembersService.deltaSyncGroup(sequence, groupId, userId);
  }

  // @Get(':groupId/messages')
  // @UseInterceptors(ImageUrlInterceptor)
  // @UsePermanentUrls(['imageUrl'])
  // async getGroupMessagesWithKey(
  //   @Param('groupId') groupId: string,
  //   @Req() req: Request,
  // ) {
  //   (req as any).res.locals.message = 'Group details fetched successfully';

  //   const memberId = req.user?.id;

  //   const groupIdNum = Number(groupId);
  //   if (isNaN(groupIdNum)) {
  //     throw new BadRequestException('Invalid groupId');
  //   }

  //   if (!memberId) {
  //     throw new UnauthorizedException('Member ID missing in user payload');
  //   }

  //   return await this.groupMembersService.getGroupMessagesWithKey(
  //     groupIdNum,
  //     memberId,
  //   );
  // }

  @Get('encryption-keys/:memberId')
  @ApiOperation({ summary: 'Get all group encryption keys for a member' })
  @ApiParam({ name: 'memberId', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Encryption keys fetched successfully',
  })
  async getEncryptionKeysForMember(
    @Param('memberId', ParseIntPipe) memberId: number,
    @Req() req: Request,
  ) {
    const userId = req.user?.id;
    if (Number(userId) !== Number(memberId)) {
      throw new UnauthorizedException();
    }

    return this.groupMembersService.getAllGroupEncryptionKeysForMember(
      memberId,
    );
  }

  @Get(':groupId/members')
  @UseInterceptors(ImageUrlInterceptor)
  @ApiOperation({ summary: 'Get all members of a specific group' })
  @ApiParam({ name: 'groupId', type: Number })
  @ApiResponse({
    status: 200,
    description: 'Group members returned successfully',
  })
  @ApiResponse({ status: 404, description: 'Group not found' })
  async getGroupMembers(@Param('groupId', ParseIntPipe) groupId: number) {
    try {
      const {
        groupId: id,
        groupName,
        groupImageUrl,
        members,
      } = await this.groupMembersService.getGroupMemberDetails(groupId);
      return { groupId: id, groupName, groupImageUrl, members };
    } catch (error) {
      if (error.message === 'Group not found') {
        throw new NotFoundException('Group not found');
      }
      throw error;
    }
  }

  @Get('status/:memberId')
  @ApiOperation({ summary: 'Get membership status of a member in a group' })
  @ApiParam({ name: 'memberId', type: Number })
  @ApiQuery({ name: 'groupId', type: Number, description: 'ID of the group' })
  @ApiResponse({
    status: 200,
    description: 'Membership status returned successfully',
  })
  @ApiResponse({ status: 400, description: 'Invalid groupId or memberId' })
  async getMembershipStatus(
    @Query('groupId') groupId: string,
    @Param('memberId') memberId: string,
  ): Promise<{ status: 'joined' | 'left' | 'not found' }> {
    const gId = parseInt(groupId, 10);
    const mId = parseInt(memberId, 10);

    if (isNaN(gId) || isNaN(mId)) {
      throw new BadRequestException('Invalid groupId or memberId');
    }

    const status = await this.groupMembersService.getMembershipStatus(gId, mId);
    return { status };
  }
}
