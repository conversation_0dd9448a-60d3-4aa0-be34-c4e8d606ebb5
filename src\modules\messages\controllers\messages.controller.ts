import { Controller, Post, Body, BadRequestException } from '@nestjs/common';
import { GroupMessagesService } from '../services/group-messages.service';
import { RetryMessageDto } from '../dto/retry-message.dto';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBody,
  ApiBearerAuth,
} from '@nestjs/swagger';

@ApiTags('Messages')
@ApiBearerAuth()
@Controller('messages')
export class MessagesController {
  constructor(private readonly groupmessageService: GroupMessagesService) {}

  @Post('retry')
  @ApiOperation({ summary: 'Retry sending a failed message' })
  @ApiBody({ type: RetryMessageDto })
  @ApiResponse({
    status: 201,
    description: 'Message successfully queued for retry',
    schema: {
      example: {
        success: true,
        messageId: 123,
        message: 'Message queued for delivery',
      },
    },
  })
  @ApiResponse({
    status: 400,
    description: 'Failed to retry message',
  })
  async retryMessage(@Body() retryDto: RetryMessageDto) {
    try {
      const savedMessage =
        await this.groupmessageService.saveAndBroadcastMessage(
          retryDto.sendMessageDto,
          true,
          retryDto.retryCount,
        );

      return {
        success: true,
        messageId: savedMessage.id,
        message: 'Message queued for delivery',
      };
    } catch (error) {
      throw new BadRequestException('Failed to retry message');
    }
  }
}
