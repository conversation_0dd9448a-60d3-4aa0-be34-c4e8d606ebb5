import { MemberFcmToken } from '../../../modules/members/entities/member-fcm-token.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  CreateDateColumn,
  OneToOne,
} from 'typeorm';
import { GroupEncryptionKey } from '../../groups/entities/group-encryption-keys.entity';
import { PairChat } from '../../chat/p2p/entities/pair-chats.entity';
import { IdentityKeyEntity } from '../../security/entities/identity-key.entity';
import { SignedPreKeyEntity } from '../../security/entities/signed-prekey.entity';
import { OneTimePreKeyEntity } from '../../security/entities/one-time-prekey.entity';
import { ChainKeyStateEntity } from '../../security/entities/chain-key-states.entity';
import { SessionStateEntity } from '../../security/entities/session-state.entity';
import { MediaEncryptionKey } from '../../media/entities/media-encryption-key.entity';
import { User } from '../../users/entities/user.entity';

@Entity('org_members')
export class OrgMember {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'org_id' })
  orgId: number;

  @Column()
  name: string;

  @Column({ name: 'phone_no', unique: true })
  phoneNo: string;

  @Column({ unique: true })
  email: string;

  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @Column({ name: 'is_verified', default: false })
  isVerified: boolean;

  @Column({ name: 'last_login_at', nullable: true, type: 'timestamp' })
  lastLoginAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @Column({ name: 'created_by' })
  createdBy: number;

  @ManyToOne('Organization', 'members')
  @JoinColumn({ name: 'org_id' })
  organization: any;

  @OneToMany('GroupMember', 'member')
  groupMemberships: any[];

  @OneToMany('OtpVerification', 'member')
  otpVerifications: any[];

  @OneToMany('GroupMessage', 'sender')
  sentMessages: any[];

  @OneToMany('GroupMessageRead', 'reader')
  readMessages: any[];

  @ManyToOne(() => User, (user) => user.createdUsers)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @OneToMany(() => MemberFcmToken, (fcmToken) => fcmToken.member)
  fcmTokens: MemberFcmToken[];

  @OneToMany(() => GroupEncryptionKey, (encryptionKey) => encryptionKey.member)
  groupEncryptionKeys: GroupEncryptionKey[];

  @OneToMany(() => PairChat, (pairChat) => pairChat.member1)
  pairChatsAsMember1: PairChat[];

  @OneToMany(() => PairChat, (pairChat) => pairChat.member2)
  pairChatsAsMember2: PairChat[];

  // Optional: get all sessions via PairChats
  @OneToMany(() => SessionStateEntity, (session) => session.pairChat)
  sessions: SessionStateEntity[];

  @OneToOne(() => IdentityKeyEntity, (identityKey) => identityKey.member)
  identityKey: IdentityKeyEntity;

  @OneToMany(() => SignedPreKeyEntity, (signedPreKey) => signedPreKey.member)
  signedPreKeys: SignedPreKeyEntity[];

  @OneToMany(() => OneTimePreKeyEntity, (oneTimePreKey) => oneTimePreKey.member)
  oneTimePreKeys: OneTimePreKeyEntity[];

  @OneToMany(() => ChainKeyStateEntity, (chainKeyState) => chainKeyState.owner)
  chainKeyStates: ChainKeyStateEntity[];

  @OneToMany(() => MediaEncryptionKey, (key) => key.recipient)
  mediaKeys: MediaEncryptionKey[];
}
