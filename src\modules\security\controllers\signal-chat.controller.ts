import {
  Controller,
  Get,
  Post,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { SignalMessagingService } from '../services/signal-messaging.service';
import { MessageEncryptionService } from '../services/message-encryption.service';

/**
 * Signal Chat Controller
 *
 * Handles HTTP requests related to Signal Protocol messaging,
 * encrypted message sending/receiving, and message management.
 */
@ApiTags('p2p-messaging')
@Controller('p2p/messages')
// @UseGuards(JwtAuthGuard)
export class SignalChatController {
  private readonly logger = new Logger(SignalChatController.name);

  constructor(
    private readonly signalMessagingService: SignalMessagingService,
    private readonly messageEncryptionService: MessageEncryptionService,
  ) {}

  /**
   * Send encrypted message
   */
  @Post('send')
  @ApiOperation({ summary: 'Send encrypted message' })
  @ApiResponse({ status: 201, description: 'Message sent successfully' })
  async sendMessage(@Body() messageData: any) {
    this.logger.log('Sending encrypted message');
    const { sessionId, senderUserId, recipientUserId, message } = messageData;
    return this.signalMessagingService.sendMessage(
      sessionId,
      senderUserId,
      recipientUserId,
      message,
    );
  }

  /**
   * Receive and decrypt message
   */
  @Post('receive')
  @ApiOperation({ summary: 'Receive and decrypt message' })
  @ApiResponse({ status: 200, description: 'Message received successfully' })
  async receiveMessage(@Body() messageData: any) {
    this.logger.log('Receiving encrypted message');
    const { sessionId, encryptedMessage, recipientUserId } = messageData;
    return this.signalMessagingService.receiveMessage(
      sessionId,
      encryptedMessage,
      recipientUserId,
    );
  }

  /**
   * Get message history for session
   */
  @Get('history/:sessionId')
  @ApiOperation({ summary: 'Get message history for session' })
  @ApiResponse({
    status: 200,
    description: 'Message history retrieved successfully',
  })
  async getMessageHistory(
    @Param('sessionId') sessionId: string,
    @Query('userId') userId: number,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    this.logger.log(`Getting message history for session ${sessionId}`);
    return this.signalMessagingService.getMessageHistory(
      sessionId,
      userId,
      limit,
      offset,
    );
  }

  /**
   * Mark message as read
   */
  @Post(':messageId/read')
  @ApiOperation({ summary: 'Mark message as read' })
  @ApiResponse({
    status: 200,
    description: 'Message marked as read successfully',
  })
  async markMessageAsRead(
    @Param('messageId') messageId: string,
    @Body() userData: any,
  ) {
    this.logger.log(`Marking message ${messageId} as read`);
    const { userId } = userData;
    return this.signalMessagingService.markMessageAsRead(messageId, userId);
  }

  /**
   * Confirm message delivery
   */
  @Post(':messageId/delivered')
  @ApiOperation({ summary: 'Confirm message delivery' })
  @ApiResponse({
    status: 200,
    description: 'Message delivery confirmed successfully',
  })
  async confirmMessageDelivery(
    @Param('messageId') messageId: string,
    @Body() userData: any,
  ) {
    this.logger.log(`Confirming delivery of message ${messageId}`);
    const { recipientUserId } = userData;
    return this.signalMessagingService.confirmMessageDelivery(
      messageId,
      recipientUserId,
    );
  }

  /**
   * Delete message
   */
  @Delete(':messageId')
  @ApiOperation({ summary: 'Delete message' })
  @ApiResponse({ status: 200, description: 'Message deleted successfully' })
  async deleteMessage(
    @Param('messageId') messageId: string,
    @Body() userData: any,
  ) {
    this.logger.log(`Deleting message ${messageId}`);
    const { userId } = userData;
    return this.signalMessagingService.deleteMessage(messageId, userId);
  }
}
