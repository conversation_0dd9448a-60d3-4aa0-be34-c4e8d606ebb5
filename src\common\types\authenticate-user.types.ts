import { EncryptedPrivateKey } from 'src/modules/security/entities/identity-key.entity';
import { AdminUserType, MemberUserType, UserType } from './user-type';

export interface BaseUser {
  id: number;
  email: string;
  orgId?: number;
  imageUrl?: string;
  type: UserType;
}

// Product Admin or Org Admin
export interface AdminUser extends BaseUser {
  type: AdminUserType;
  roleId: number;
  username: string;
  organization?: {
    id: number;
    name: string;
  };
  encryptedAdminSecretKey?: string;
  adminSecretKeyNonce?: string;
  adminSecretKeySalt?: string;
}

// Org Member
export interface MemberUser extends BaseUser {
  type: MemberUserType;
  name: string;
  organization: {
    id: number;
    name: string;
  };
  encryptedPrivateKey: EncryptedPrivateKey;
  publicKey: string;
  createdBy: number;
}

// Final union type
export type AuthenticatedUser = AdminUser | MemberUser;
