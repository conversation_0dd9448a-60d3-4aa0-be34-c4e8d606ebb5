import { Request } from 'express';

export interface ClientDeviceInfo {
  deviceName?: string;
  userAgent?: string;
  ip?: string;
}

export function extractClientDeviceInfo(req: Request): ClientDeviceInfo {
  const deviceName = req.headers['x-device-name'] as string | undefined;
  const userAgent = req.headers['user-agent'] as string | undefined;

  const rawIp = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
  const ip = Array.isArray(rawIp)
    ? rawIp[0]
    : rawIp?.toString().split(',')[0]?.trim();

  return {
    deviceName,
    userAgent,
    ip,
  };
}
