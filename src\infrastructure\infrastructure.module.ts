import { Module } from '@nestjs/common';
import { NotificationModule } from './notification/notification.module';
import { QueueModule } from './queue/queue.module';
import { RabbitMQConfigModule } from './rabbitmq/rabbitMq.module';
import { RedisModule } from './redis/redis.module';
import { SocketModule } from './socket/socket.module';
import { EventModule } from './events/event.module';
import { MonitoringModule } from './monitoring/monitoring.module';
import { CircuitBreakerModule } from './circuit-breaker/circuit-breaker.module';
import { AppThrottlerModule } from './throttler/throttler.module';

@Module({
  imports: [
    NotificationModule,
    QueueModule,
    RabbitMQConfigModule,
    RedisModule,
    SocketModule,
    EventModule,
    MonitoringModule,
    CircuitBreakerModule,
    AppThrottlerModule,
  ],
  exports: [
    NotificationModule,
    QueueModule,
    RabbitMQConfigModule,
    RedisModule,
    SocketModule,
    EventModule,
    MonitoringModule,
    CircuitBreakerModule,
    AppThrottlerModule,
  ],
})
export class InfrastructureModule {}
