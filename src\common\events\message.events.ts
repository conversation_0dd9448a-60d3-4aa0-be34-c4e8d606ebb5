import {
  MemberNotificationInfo,
  PushNotification,
} from 'src/infrastructure/notification/services/notification.service';
import { EVENT_NAMES } from '../constants/event-names';
import { BaseEvent } from './base.event';

export class MessageSentEvent extends BaseEvent {
  readonly name = EVENT_NAMES.MESSAGE_SENT;

  constructor(
    public readonly messageId: number,
    public readonly senderId: number,
    public readonly memberNotificationInfo: MemberNotificationInfo[],
    public readonly notificationPayload: PushNotification,
    public readonly retryCount: number,
  ) {
    super();
  }
}

export class MessageDeliveryAttemptEvent extends BaseEvent {
  readonly name = EVENT_NAMES.MESSAGE_DELIVERY_ATTEMPT;

  constructor(public readonly messageId: number) {
    super();
  }
}

export class MessageDeliveryFailedEvent extends BaseEvent {
  readonly name = EVENT_NAMES.MESSAGE_DELIVERY_FAILED;

  constructor(
    public readonly messageId: number,
    public readonly reason: string,
  ) {
    super();
  }
}

export class MessageDeliverySuccessEvent extends BaseEvent {
  readonly name = EVENT_NAMES.MESSAGE_DELIVERY_SUCCESS;

  constructor(
    public readonly messageId: number,
    public readonly deliveredToCount: number,
  ) {
    super();
  }
}
