import {
  <PERSON>,
  Post,
  Body,
  Req,
  Res,
  Get,
  HttpCode,
  HttpStatus,
  UseInterceptors,
  UnauthorizedException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { Request, Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
} from '@nestjs/swagger';
import { AuthService } from '../services/auth.service';
import { OtpService } from '../services/otp.service';
import { AdminLoginDto, AdminResponseDto, RefreshTokenDto } from '../dto';
import { MobileLogoutDto } from '../dto/mobile-logout.dto';
import { Public } from '../../../common/decorators/public.decorator';
import {
  AdminRefreshToken,
  MemberRefreshToken,
} from '../interfaces/refresh-token-payload.interface';
import { ConfigService } from '@nestjs/config';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';
import { UsePermanentUrls } from '../../../common/decorators/image.decorator';
import { USER_TYPE } from 'src/common/constants/user-type.constants';
import { AuthenticatedUser } from 'src/common/types/authenticate-user.types';
import { TokenService } from '../services/token.service';
import {
  ClientDeviceInfo,
  extractClientDeviceInfo,
} from 'src/common/utils/user.device-info';
import {
  RefreshChallengeDto,
  RegisterDeviceKeyDto,
} from '../dto/register-device-key.dto';
import { UserType } from 'src/common/types/user-type';
import { RedisService } from 'src/infrastructure/redis/services/redis.service';
import { randomBytes } from 'crypto';
import { RateLimit } from 'src/common/decorators/rate-limit.decorator';

@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);
  constructor(
    private readonly authService: AuthService,
    private readonly otpService: OtpService,
    private readonly configService: ConfigService,
    private readonly tokenService: TokenService,
    private readonly redisService: RedisService,
  ) {}

  @Public()
  @Post('login-challenge')
  @RateLimit({ limit: 10, ttl: 60, name: 'login-challenge' })
  @HttpCode(HttpStatus.OK)
  async createLoginChallenge(
    @Body() body: { username: string; publicKey: string },
  ) {
    const challenge = randomBytes(32).toString('base64url');
    const expiresIn = 60;

    await this.redisService.storeLoginChallenge(
      challenge,
      body.username,
      body.publicKey,
      expiresIn,
    );

    return {
      challenge,
      expiresIn,
      message: 'Challenge generated successfully',
    };
  }

  /**
   * Login endpoint for both ProductAdmin and OrgAdmin
   */
  @Public()
  @Post('login')
  @RateLimit({ limit: 5, ttl: 300, name: 'login-attempt' })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Login endpoint for Admin users' })
  @ApiBody({ type: AdminLoginDto })
  @ApiResponse({
    status: 200,
    description: 'Login successful',
    type: AdminResponseDto,
  })
  async login(
    @Body() loginDto: AdminLoginDto,
    @Res({ passthrough: true }) response: Response,
    @Req() req: Request,
  ) {
    const challengeData = await this.redisService.getAndValidateLoginChallenge(
      loginDto.challenge,
    );

    if (!challengeData || challengeData.username !== loginDto.username) {
      throw new UnauthorizedException('Invalid challenge');
    }

    const isSignatureValid = await this.tokenService.verifySignature(
      loginDto.challenge,
      loginDto.signature,
      loginDto.publicKey,
    );
    if (!isSignatureValid) {
      throw new UnauthorizedException('Invalid Signature');
    }

    const result = await this.authService.login(loginDto, req);

    this.setRefreshTokenCookie(response, result.refreshToken);
    this.setAccessTokenCookie(response, result.accessToken);

    response.locals.message = 'Login successfully';
    const { refreshToken, accessToken, ...responseData } = result;
    return responseData;
  }

  @Post('device-key')
  @RateLimit({ limit: 3, ttl: 300, name: 'device-register' })
  @HttpCode(HttpStatus.CREATED)
  async registerDeviceKey(
    @Body() registerDeviceKeyDto: RegisterDeviceKeyDto,
    @Req() request: Request,
  ) {
    const signature = request.headers['x-signature'] as string;
    const timestamp = request.headers['x-timestamp'] as string;
    const headerPublicKey = request.headers['x-public-key'] as string;

    // 1. Verify the required headers are present
    if (!signature || !timestamp || !headerPublicKey) {
      throw new UnauthorizedException('Missing required security headers');
    }

    // 3. Verify the timestamp is recent (prevent replay attacks)
    const timestampValue = parseInt(timestamp);
    if (isNaN(timestampValue)) {
      throw new UnauthorizedException('Invalid timestamp format');
    }

    const now = Date.now();
    const timestampAge = Math.abs(now - timestampValue);
    const maxAge = 1 * 60 * 1000; // 2 minutes maximum age
    const maxClockSkew = 30 * 1000; // 30 seconds maximum clock difference

    // Check if request is too old OR client clock is too far ahead
    if (timestampAge > maxAge || timestampValue > now + maxClockSkew) {
      throw new UnauthorizedException('Request timestamp is invalid');
    }
    // 4. Verify the public key in header matches the one in body
    if (headerPublicKey !== registerDeviceKeyDto.publicKey) {
      throw new UnauthorizedException('Public key mismatch');
    }

    // 5. Verify the cryptographic signature
    // This is the most important security check
    const isSignatureValid = await this.tokenService.verifySignature(
      timestamp,
      signature,
      headerPublicKey,
    );

    if (!isSignatureValid) {
      throw new UnauthorizedException('Invalid signature');
    }

    const refreshToken =
      request.cookies?.refresh_token || registerDeviceKeyDto.refreshToken;

    if (refreshToken) {
      try {
        const decoded = (await this.tokenService.decodeRefreshToken(
          refreshToken,
        )) as AdminRefreshToken | MemberRefreshToken;

        await this.redisService.storePublicKey(
          decoded.type as UserType,
          decoded.sub,
          decoded.tokenId,
          registerDeviceKeyDto.publicKey,
        );
      } catch (error) {
        console.log(error);
        throw new UnauthorizedException('Invalid refresh token');
      }
    }

    return {
      statusCode: HttpStatus.CREATED,
      message: 'Device public key registered successfully',
    };
  }

  @Get('user-details')
  @RateLimit({ limit: 30, ttl: 60, name: 'user-details' })
  @UseInterceptors(ImageUrlInterceptor)
  @UsePermanentUrls(['imageUrl'])
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Get authenticated user details' })
  @ApiResponse({
    status: 200,
    description: 'User details fetched successfully',
    type: AdminResponseDto,
  })
  async getMe(@Req() req: Request): Promise<AdminResponseDto> {
    (req as any).res.locals.message = 'User details fetched successfully';

    const user: AuthenticatedUser = req.user;
    return this.authService.getUserDetails(user);
  }

  /**
   * Generate a challenge for refresh token authentication
   * This implements a proper challenge-response flow
   */
  @Public()
  @Post('refresh-challenge')
  @RateLimit({ limit: 20, ttl: 60, name: 'refresh-challenge' })
  @HttpCode(HttpStatus.OK)
  async getRefreshChallenge(
    @Body() refreshChallengeDto: RefreshChallengeDto,
    @Req() request: Request,
  ) {
    try {
      const challenge = randomBytes(32).toString('base64url');

      // 1. Extract refresh token from cookies
      const refreshToken =
        request.cookies?.refresh_token || refreshChallengeDto?.refreshToken;

      if (refreshToken) {
        try {
          const decoded = (await this.tokenService.decodeRefreshToken(
            refreshToken,
          )) as AdminRefreshToken | MemberRefreshToken;

          await this.redisService.storeRefreshChallenge(
            challenge,
            decoded.type as UserType,
            decoded.sub,
            decoded.tokenId,
          );
        } catch (error) {
          console.log(error);
          throw new UnauthorizedException('Invalid refresh token');
        }
      }

      // 5. Return the challenge to the client
      return challenge;
    } catch (error) {
      this.logger.error('Failed to generate refresh challenge:', error);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * Refresh token endpoint
   */
  @Public()
  @Post('refresh')
  @RateLimit({ limit: 10, ttl: 60, name: 'token-refresh' })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh access and refresh tokens' })
  @ApiBody({ type: RefreshTokenDto })
  @ApiResponse({
    status: 200,
    description: 'Tokens refreshed successfully',
    schema: { example: { accessToken: 'string', refreshToken: 'string' } },
  })
  @ApiResponse({
    status: 401,
    description: 'Refresh token is missing or invalid',
  })
  async refresh(
    @Body() refreshTokenDto: RefreshTokenDto,
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
  ) {
    const refreshToken =
      request.cookies?.refresh_token || refreshTokenDto.refreshToken;

    if (!refreshToken) {
      return {
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Refresh token is required',
      };
    }

    // Extract device info from request
    const deviceInfo: ClientDeviceInfo = extractClientDeviceInfo(request);

    const tokens = await this.tokenService.refreshToken(refreshToken, request);

    if (deviceInfo.deviceName) {
      return {
        accessToken: tokens.accessToken,
        refreshToken: tokens.refreshToken,
      };
    } else {
      this.clearRefreshTokenCookie(response);
      this.clearAccessTokenCookie(response);

      this.setRefreshTokenCookie(response, tokens.refreshToken);
      this.setAccessTokenCookie(response, tokens.accessToken);

      return {
        message: 'Tokens refreshed and stored in cookies',
      };
    }
  }

  @Public()
  @Post('logout')
  @RateLimit({ limit: 10, ttl: 60, name: 'logout' })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout current user' })
  @ApiResponse({
    status: 200,
    description: 'Logged out successfully',
    schema: { example: { message: 'Logged out successfully' } },
  })
  async logout(
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
  ) {
    const refreshToken = request.cookies?.refresh_token;

    if (refreshToken) {
      try {
        const decoded = (await this.tokenService.decodeRefreshToken(
          refreshToken,
        )) as AdminRefreshToken;

        await this.authService.logout(
          decoded.sub,
          decoded.type,
          decoded.tokenId,
        );
        await this.redisService.removePublicKey(
          decoded.type as UserType,
          decoded.sub,
          decoded.tokenId,
        );
      } catch (error) {
        console.log(error);
      }
    }

    this.clearRefreshTokenCookie(response);
    this.clearAccessTokenCookie(response);

    return { message: 'Logged out successfully' };
  }

  @Public()
  @Post('logout-all')
  @RateLimit({ limit: 3, ttl: 300, name: 'logout-all' })
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: 'Logout from all devices' })
  @ApiResponse({
    status: 200,
    description: 'Logged out from all devices successfully',
    schema: {
      example: { message: 'Logged out from all devices successfully' },
    },
  })
  async logoutAll(
    @Req() request: Request,
    @Res({ passthrough: true }) response: Response,
  ) {
    const refreshToken = request.cookies?.refresh_token;

    if (refreshToken) {
      try {
        const decoded = (await this.tokenService.decodeRefreshToken(
          refreshToken,
        )) as AdminRefreshToken;

        await this.authService.logoutAll(decoded.sub, decoded.type);
      } catch (error) {
        console.log(error);
      }
    }

    this.clearRefreshTokenCookie(response);

    return { message: 'Logged out from all devices successfully' };
  }

  @Public()
  @Post('mobile/logout')
  @RateLimit({ limit: 10, ttl: 60, name: 'mobile-logout' })
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Logout mobile session using refresh token' })
  @ApiBody({ type: MobileLogoutDto })
  @ApiResponse({
    status: 200,
    description: 'Mobile logout successful',
    schema: { example: { message: 'Logged out successfully' } },
  })
  @ApiResponse({ status: 400, description: 'Refresh token is required' })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async mobileLogout(@Body() body: MobileLogoutDto) {
    const { refreshToken, memberId, deviceId } = body;

    if (!refreshToken) {
      throw new BadRequestException('Refresh token is required');
    }

    try {
      const decoded = (await this.tokenService.decodeRefreshToken(
        refreshToken,
      )) as MemberRefreshToken;

      if (decoded.type !== USER_TYPE.ORG_MEMBER) {
        throw new UnauthorizedException('Invalid token type');
      }

      const tokenId = decoded.tokenId;

      // Perform logout
      await this.otpService.logout(memberId, deviceId);
      await this.authService.logout(memberId, USER_TYPE.ORG_MEMBER, tokenId);
      await this.redisService.removePublicKey(
        decoded.type as UserType,
        decoded.sub,
        decoded.tokenId,
      );

      return { message: 'Logged out successfully' };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /** Helper methods for cookie management */
  setRefreshTokenCookie(response: Response, token: string): void {
    const refreshExpiresInDays = parseInt(
      this.configService
        .get<string>('jwt.refreshExpiresIn', '7d')
        .replace('d', '') || '7',
    );
    const sameSite = this.configService.get<'lax' | 'strict' | 'none'>(
      'COOKIE_SAMESITE',
      'lax',
    );

    response.cookie('refresh_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV?.trim() === 'production',
      sameSite,
      maxAge: refreshExpiresInDays * 24 * 60 * 60 * 1000,
    });
  }

  setAccessTokenCookie(response: Response, token: string) {
    const rawExpiry = this.configService.get<string>('jwt.expiresIn', '1d');

    let maxAgeMs: number;

    if (rawExpiry.endsWith('d')) {
      const days = parseInt(rawExpiry.replace('d', ''), 10) || 1;
      maxAgeMs = days * 24 * 60 * 60 * 1000;
    } else if (rawExpiry.endsWith('h')) {
      const hours = parseInt(rawExpiry.replace('h', ''), 10) || 1;
      maxAgeMs = hours * 60 * 60 * 1000;
    } else if (rawExpiry.endsWith('m')) {
      const minutes = parseInt(rawExpiry.replace('m', ''), 10) || 1;
      maxAgeMs = minutes * 60 * 1000;
    } else {
      // fallback: treat as days if no unit given
      const days = parseInt(rawExpiry, 10) || 1;
      maxAgeMs = days * 24 * 60 * 60 * 1000;
    }

    const sameSite = this.configService.get<'lax' | 'strict' | 'none'>(
      'COOKIE_SAMESITE',
      'lax',
    );

    response.cookie('access_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV?.trim() === 'production',
      sameSite,
      maxAge: maxAgeMs,
    });
  }

  private clearRefreshTokenCookie(response: Response): void {
    response.clearCookie('refresh_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV?.trim() === 'production',
    });
  }

  private clearAccessTokenCookie(response: Response): void {
    response.clearCookie('access_token', {
      httpOnly: true,
      secure: process.env.NODE_ENV?.trim() === 'production',
    });
  }
}
