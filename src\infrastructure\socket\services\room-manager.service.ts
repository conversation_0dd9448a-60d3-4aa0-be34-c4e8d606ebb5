import { Injectable, Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';

export enum RoomType {
  MEMBER = 'member', // Individual member room (member_123)
  GROUP = 'group', // Group room (group_456)
  PRIVATE = 'private', // Private chat room (private_123_456)
  ORGANIZATION = 'organization', // Organization room (organization_789)
}

@Injectable()
export class RoomManagerService {
  private readonly logger = new Logger(RoomManagerService.name);

  /**
   * Generate room name based on type and identifiers
   */
  generateRoomName(
    type: RoomType,
    ...identifiers: (string | number)[]
  ): string {
    switch (type) {
      case RoomType.MEMBER:
        return `member_${identifiers[0]}`;

      case RoomType.GROUP:
        return `group_${identifiers[0]}`;

      case RoomType.PRIVATE:
        // Sort identifiers to ensure consistent room naming (private_123_456 same as private_456_123)
        const sortedIds = identifiers
          .map((id) => Number(id))
          .sort((a, b) => a - b);
        return `private_${sortedIds.join('_')}`;

      case RoomType.ORGANIZATION:
        return `organization_${identifiers[0]}`;

      default:
        throw new Error(`Unknown room type: ${type}`);
    }
  }

  /**
   * Join a client to a room
   */
  async joinRoom(
    client: Socket,
    roomType: RoomType,
    ...identifiers: (string | number)[]
  ): Promise<string> {
    const roomName = this.generateRoomName(roomType, ...identifiers);

    await client.join(roomName);

    this.logger.log(
      `Client ${client.id} (member: ${client.data.memberId}) joined ${roomType} room: ${roomName}`,
    );

    return roomName;
  }

  /**
   * Leave a room
   */
  async leaveRoom(
    client: Socket,
    roomType: RoomType,
    ...identifiers: (string | number)[]
  ): Promise<string> {
    const roomName = this.generateRoomName(roomType, ...identifiers);

    await client.leave(roomName);

    this.logger.log(
      `Client ${client.id} (member: ${client.data.memberId}) left ${roomType} room: ${roomName}`,
    );

    return roomName;
  }

  /**
   * Join multiple rooms at once
   */
  async joinMultipleRooms(
    client: Socket,
    rooms: { type: RoomType; identifiers: (string | number)[] }[],
  ): Promise<string[]> {
    const joinedRooms: string[] = [];

    for (const room of rooms) {
      const roomName = await this.joinRoom(
        client,
        room.type,
        ...room.identifiers,
      );
      joinedRooms.push(roomName);
    }

    return joinedRooms;
  }

  /**
   * Leave multiple rooms at once
   */
  async leaveMultipleRooms(
    client: Socket,
    rooms: { type: RoomType; identifiers: (string | number)[] }[],
  ): Promise<string[]> {
    const leftRooms: string[] = [];

    for (const room of rooms) {
      const roomName = await this.leaveRoom(
        client,
        room.type,
        ...room.identifiers,
      );
      leftRooms.push(roomName);
    }

    return leftRooms;
  }

  /**
   * Emit to a specific room
   */
  emitToRoom(
    server: Server,
    roomType: RoomType,
    event: string,
    data: any,
    ...identifiers: (string | number)[]
  ): void {
    const roomName = this.generateRoomName(roomType, ...identifiers);
    server.to(roomName).emit(event, data);

    this.logger.debug(`Emitted '${event}' to ${roomType} room: ${roomName}`);
  }

  /**
   * Get clients count in a room
   */
  async getRoomClientsCount(
    server: Server,
    roomType: RoomType,
    ...identifiers: (string | number)[]
  ): Promise<number> {
    const roomName = this.generateRoomName(roomType, ...identifiers);
    const sockets = await server.in(roomName).fetchSockets();
    return sockets.length;
  }

  /**
   * Get all rooms a client is in
   */
  getClientRooms(client: Socket): string[] {
    return Array.from(client.rooms);
  }

  /**
   * Check if client is in a specific room
   */
  isClientInRoom(
    client: Socket,
    roomType: RoomType,
    ...identifiers: (string | number)[]
  ): boolean {
    const roomName = this.generateRoomName(roomType, ...identifiers);
    return client.rooms.has(roomName);
  }
}
