import {
  Body,
  Controller,
  Delete,
  HttpCode,
  HttpStatus,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Req,
  Res,
  UnauthorizedException,
  UseInterceptors,
} from '@nestjs/common';
import { UsersService } from '../services/users.service';
import { ConfigService } from '@nestjs/config';
import { CreateUserDto } from '../dto/create-users.dto';
import { User } from '../entities/user.entity';
import { Response, Request } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { CloudStorageInterceptor } from '../../../common/interceptors/cloud-storage.interceptor';
import { Headers } from '@nestjs/common';
import { UpdateUserDto } from '../dto/update.users.dto';
import { ChangePasswordDto } from '../dto/change-password.dto';
import { UpdateOrganizationDto } from '../../organization/dto';
import { Organization } from '../../organization/entities/organization.entity';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';
import { ProductAdminOnly } from 'src/common/decorators/roles.decorator';

@ApiTags('Users') // Groups all endpoints under "Users"
@ApiBearerAuth() // Marks this controller as requiring JWT auth
@Controller('users')
export class UserController {
  constructor(
    private readonly usersService: UsersService,
    private readonly configService: ConfigService,
  ) {}

  @Post('create-admin')
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Create an admin with organization' })
  @ApiResponse({
    status: 200,
    description: 'Admin created successfully (password removed from response).',
    type: User,
  })
  @ApiBody({ type: CreateUserDto })
  async createWithOrganization(
    @Body() createUserDto: CreateUserDto,
    @Res({ passthrough: true }) response: Response,
  ): Promise<Partial<User>> {
    const user = await this.usersService.createWithOrganization(createUserDto);

    response.locals.message = 'Admin created successfully';

    // Remove password before returning
    const { password, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  @Put(':id')
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update user profile' })
  @ApiParam({ name: 'id', type: Number, description: 'User ID' })
  @ApiBody({ type: UpdateUserDto })
  @ApiResponse({
    status: 200,
    description: 'User updated successfully.',
    type: User,
  })
  async updateUser(
    @Param('id', ParseIntPipe) id: number,
    @Res({ passthrough: true }) response: Response,
    @Body() updateUserDto: UpdateUserDto,
    @Req() request: Request,
  ): Promise<Partial<User>> {
    const user = request.user;

    if (user.id !== id) {
      throw new UnauthorizedException();
    }

    const result = await this.usersService.update(id, updateUserDto);

    response.locals.message = 'User Updated successfully';

    const { password, ...userWithoutPassword } = result;
    return userWithoutPassword;
  }

  @Put(':id/change-password')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Change user password' })
  @ApiParam({ name: 'id', type: Number, description: 'User ID' })
  @ApiBody({ type: ChangePasswordDto })
  @ApiResponse({
    status: 200,
    description: 'Password changed successfully.',
  })
  async changePassword(
    @Param('id', ParseIntPipe) id: number,
    @Body() changePasswordDto: ChangePasswordDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<Partial<User> | { message: string }> {
    const user = request.user;
    if (user.id !== id) {
      throw new UnauthorizedException();
    }
    const result = await this.usersService.changePassword(
      id,
      changePasswordDto,
    );
    response.locals.message = 'Password changed successfully';
    return result;
  }

  @Delete(':id')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Delete a user by ID' })
  @ApiParam({ name: 'id', type: Number, description: 'User ID' })
  @ApiResponse({ status: 200, description: 'User deleted successfully.' })
  async deleteUser(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ message: string }> {
    return this.usersService.remove(id);
  }

  @Put('organizations/:org_id')
  @ProductAdminOnly()
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Update organization details' })
  @ApiParam({ name: 'org_id', type: Number, description: 'Organization ID' })
  @ApiBody({ type: UpdateOrganizationDto })
  @ApiResponse({
    status: 200,
    description: 'Organization updated successfully.',
    type: Organization,
  })
  update(
    @Param('org_id', ParseIntPipe) id: number,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @Res({ passthrough: true }) response: Response,
  ): Promise<Organization> {
    response.locals.message = 'Organization updated successfully';
    return this.usersService.updateWithOrganization(id, updateOrganizationDto);
  }
}
