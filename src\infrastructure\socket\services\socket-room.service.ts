import { Injectable } from '@nestjs/common';
import { SocketGateway } from '../socket.gateway';
import { ISocketRoomService } from '../interfaces/socket-room.interface';
import { RoomType } from './room-manager.service';

@Injectable()
export class SocketRoomService implements ISocketRoomService {
  constructor(private readonly socketGateway: SocketGateway) {}

  broadcastToMember(memberId: number, event: string, data: any): void {
    this.socketGateway.broadcastToMember(memberId, event, data);
  }

  emitToGroup(groupId: number, event: string, data: any): void {
    this.socketGateway.emitToGroup(groupId, event, data);
  }

  emitToPrivateChat(
    member1Id: number,
    member2Id: number,
    event: string,
    data: any,
  ): void {
    this.socketGateway.emitToPrivateChat(member1Id, member2Id, event, data);
  }

  emitToOrganization(organizationId: number, event: string, data: any): void {
    this.socketGateway.emitToOrganization(organizationId, event, data);
  }

  async getConnectedClientsCount(): Promise<number> {
    return this.socketGateway.getConnectedClientsCount();
  }

  async getRoomClientsCount(
    roomType: string,
    ...identifiers: (string | number)[]
  ): Promise<number> {
    const type = RoomType[roomType.toUpperCase() as keyof typeof RoomType];
    return this.socketGateway.getRoomStats(type, ...identifiers);
  }
}
