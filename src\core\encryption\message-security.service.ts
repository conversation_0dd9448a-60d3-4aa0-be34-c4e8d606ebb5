import { Injectable } from '@nestjs/common';
import { EncryptionService } from './encryption.service';

/**
 * MessageSecurity implements Signal Protocol-like security for group messaging
 * This is a simplified version, a complete Signal Protocol implementation
 * would require significantly more complexity
 */
@Injectable()
export class MessageSecurityService {
  constructor(private readonly encryptionService: EncryptionService) {}

  /**
   * Initialize a user with all necessary keys
   */
  initializeUser(password: string) {
    // Generate identity key pair for encryption
    const identityKeyPair = this.encryptionService.generateKeyPair();

    // Generate signing key pair for message authentication
    const signingKeyPair = this.encryptionService.generateSigningKeyPair();

    // Generate prekeys for future sessions
    const prekeys = this.encryptionService.generatePrekeys(100);

    // Encrypt private keys with user's password
    const encryptedIdentityKey = this.encryptionService.encryptSecretKey(
      identityKeyPair.secretKey,
      password,
    );

    const encryptedSigningKey = this.encryptionService.encryptSecretKey(
      signingKeyPair.secretKey,
      password,
    );

    // Return all materials needed for user initialization
    return {
      publicIdentityKey: identityKeyPair.publicKey,
      publicSigningKey: signingKeyPair.publicKey,
      encryptedIdentityKey,
      encryptedSigningKey,
      prekeys,
    };
  }

  /**
   * Create a new encrypted group conversation
   */
  createGroupConversation(
    creatorSecretKey: string,
    memberPublicKeys: string[],
  ) {
    // Generate a random group key
    const groupKey = this.encryptionService.generateGroupKey();

    // Encrypt the group key for each member
    const encryptedGroupKeys = memberPublicKeys.map((memberPublicKey) => {
      return {
        recipientPublicKey: memberPublicKey,
        encryptedGroupKey: this.encryptionService.encryptGroupKeyForUser(
          groupKey,
          memberPublicKey,
          creatorSecretKey,
        ),
      };
    });

    // Generate a unique ID for this conversation
    const conversationId = this.generateConversationId();

    return {
      conversationId,
      encryptedGroupKeys,
      // Initial chain key for forward secrecy
      chainKey: this.encryptionService.generateGroupKey(),
      // Iteration counter for the ratchet
      iteration: 0,
    };
  }

  /**
   * Add a new member to an existing group
   */
  addGroupMember(
    adminSecretKey: string,
    newMemberPublicKey: string,
    currentGroupKey: string,
  ) {
    // Encrypt the current group key for the new member
    const encryptedGroupKey = this.encryptionService.encryptGroupKeyForUser(
      currentGroupKey,
      newMemberPublicKey,
      adminSecretKey,
    );

    return {
      recipientPublicKey: newMemberPublicKey,
      encryptedGroupKey,
    };
  }

  /**
   * Rotate the group key (should be done periodically or when a member leaves)
   */
  rotateGroupKey(adminSecretKey: string, memberPublicKeys: string[]) {
    // Generate a new group key
    const newGroupKey = this.encryptionService.generateGroupKey();

    // Encrypt the new group key for each remaining member
    const encryptedGroupKeys = memberPublicKeys.map((memberPublicKey) => {
      return {
        recipientPublicKey: memberPublicKey,
        encryptedGroupKey: this.encryptionService.encryptGroupKeyForUser(
          newGroupKey,
          memberPublicKey,
          adminSecretKey,
        ),
      };
    });

    return {
      encryptedGroupKeys,
    };
  }

  /**
   * Prepare a message for sending to a group
   * Implements ratcheting for forward secrecy
   */
  prepareGroupMessage(
    message: string,
    groupKey: string,
    senderSigningKey: string,
    chainKey: string,
    iteration: number,
  ) {
    // Derive the next message key from the chain
    const messageKey = this.deriveMessageKey(chainKey, iteration);

    // Encrypt the actual message with the message key
    const encryptedMessage = this.encryptionService.encryptMessage(
      message,
      messageKey,
    );

    // Sign the encrypted message with the sender's signing key
    const signature = this.encryptionService.signMessage(
      JSON.stringify({
        encryptedMessage,
        iteration,
      }),
      senderSigningKey,
    );

    // Derive next chain key for future messages
    const nextChainKey = this.encryptionService.deriveNextKey(chainKey, 'next');

    return {
      encryptedMessage,
      signature,
      iteration: iteration + 1,
      nextChainKey,
    };
  }

  /**
   * Receive and decrypt a group message
   */
  decryptGroupMessage(
    encryptedMessagePackage: {
      encryptedMessage: string;
      signature: string;
      iteration: number;
    },
    groupKey: string,
    senderSigningPublicKey: string,
    chainKey: string,
  ) {
    // Verify the message signature
    const isValid = this.encryptionService.verifySignature(
      JSON.stringify({
        encryptedMessage: encryptedMessagePackage.encryptedMessage,
        iteration: encryptedMessagePackage.iteration - 1, // -1 because we incremented in prepareGroupMessage
      }),
      encryptedMessagePackage.signature,
      senderSigningPublicKey,
    );

    if (!isValid) {
      throw new Error('Message signature verification failed');
    }

    // Derive the message key used for this specific message
    const messageKey = this.deriveMessageKey(
      chainKey,
      encryptedMessagePackage.iteration - 1,
    );

    // Decrypt the message content
    const decryptedMessage = this.encryptionService.decryptMessage(
      encryptedMessagePackage.encryptedMessage,
      messageKey,
    );

    return {
      message: decryptedMessage,
      // Return the updated iteration for the chain
      iteration: encryptedMessagePackage.iteration,
      // Derive the next chain key for future messages
      nextChainKey: this.encryptionService.deriveNextKey(chainKey, 'next'),
    };
  }

  /**
   * Derive a message key from the chain key and iteration
   * Part of the Double Ratchet Algorithm
   */
  private deriveMessageKey(chainKey: string, iteration: number): string {
    let currentKey = chainKey;

    // Derive the key for this specific message
    for (let i = 0; i < iteration; i++) {
      currentKey = this.encryptionService.deriveNextKey(currentKey, `msg-${i}`);
    }

    return currentKey;
  }

  /**
   * Generate a unique conversation ID
   */
  private generateConversationId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  }
}
