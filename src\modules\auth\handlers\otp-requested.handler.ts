import { Injectable, Logger } from '@nestjs/common';
import { OtpRequestedEvent } from '../../../common/events';
import { OtpProducer } from '../../../infrastructure/rabbitmq/producers';
import { OnEvent } from '@nestjs/event-emitter';
import { EVENT_NAMES } from 'src/common/constants/event-names';

@Injectable()
export class OtpRequestedHandler {
  private readonly logger = new Logger(OtpRequestedHandler.name);

  constructor(private readonly otpProducer: OtpProducer) {}

  @OnEvent(EVENT_NAMES.OTP_REQUESTED, { async: true })
  async handle(event: OtpRequestedEvent) {
    this.logger.log(`Sending OTP to ${event.phoneNumber}: ${event.otpCode}`);
    await this.otpProducer.sendOtpTask({
      phoneNumber: event.phoneNumber,
      otp: event.otpCode,
    });
  }
}
