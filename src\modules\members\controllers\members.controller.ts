import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
  Res,
  UseInterceptors,
  Put,
  Req,
  UnauthorizedException,
} from '@nestjs/common';
import { MembersService } from '../services/members.service';
import { MemberFcmToken } from '../entities/member-fcm-token.entity';
import { CreateMemberDto } from '../dto/create-member.dto';
import { OrgMember } from '../entities/org-member.entity';
import { Response, Request } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { CloudStorageInterceptor } from '../../../common/interceptors/cloud-storage.interceptor';
import { UpdateMemberDto } from '../dto/update-member.dto';
import { CreateFcmTokenDto } from '../dto/create-fcm.dto';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';
import { MemberResponseDto } from '../dto/member-response.dto';

import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
  ApiResponse,
} from '@nestjs/swagger';

@ApiTags('Members')
@Controller('members')
export class MembersController {
  constructor(private readonly membersService: MembersService) {}

  @Post('create/:userId')
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Create a new member' })
  @ApiParam({
    name: 'userId',
    type: Number,
    description: 'ID of the user creating the member',
  })
  @ApiBody({ type: CreateMemberDto })
  @ApiResponse({
    status: 200,
    description: 'Member created successfully',
    type: OrgMember,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createMember(
    @Param('userId') userId: number,
    @Body() createMemberDto: CreateMemberDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<OrgMember> {
    const user = request.user;
    if (user.id !== Number(userId)) {
      throw new UnauthorizedException();
    }

    const member = await this.membersService.create(createMemberDto, userId);
    response.locals.message = 'Member created successfully';
    return member;
  }

  @Get()
  @UseInterceptors(ImageUrlInterceptor)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Get all members in an organization' })
  @ApiQuery({
    name: 'orgId',
    type: Number,
    required: true,
    description: 'Organization ID',
  })
  @ApiResponse({
    status: 200,
    description: 'List of members',
    type: [OrgMember],
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  findAll(
    @Query('orgId') orgId: number,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<OrgMember[]> {
    const user = request.user;
    if (user.orgId !== Number(orgId)) {
      throw new UnauthorizedException();
    }
    response.locals.message = 'Members fetched successfully';
    return this.membersService.findAll(orgId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get a member by ID' })
  @ApiParam({ name: 'id', type: Number, description: 'Member ID' })
  @ApiResponse({
    status: 200,
    description: 'Member fetched successfully',
    type: OrgMember,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  findOne(
    @Param('id', ParseIntPipe) id: number,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<OrgMember> {
    const user = request.user;
    if (user.id !== Number(id)) {
      throw new UnauthorizedException();
    }
    response.locals.message = 'Member fetched successfully';
    return this.membersService.findOne(id);
  }

  @Put(':id')
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(FileInterceptor('image'), CloudStorageInterceptor)
  @ApiOperation({ summary: 'Update a member' })
  @ApiParam({ name: 'id', type: Number, description: 'Member ID' })
  @ApiBody({ type: UpdateMemberDto })
  @ApiResponse({
    status: 200,
    description: 'Member updated successfully',
    type: MemberResponseDto,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  update(
    @Param('id', ParseIntPipe) id: number,
    @Res({ passthrough: true }) response: Response,
    @Body() updateMemberDto: UpdateMemberDto,
    @Req() request: Request,
  ): Promise<MemberResponseDto> {
    const user = request.user;
    response.locals.message = 'Member Updated successfully';
    return this.membersService.update(id, updateMemberDto, user);
  }

  @Put(':id/fcm-token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Create or update a member FCM token' })
  @ApiParam({ name: 'id', type: Number, description: 'Member ID' })
  @ApiBody({ type: CreateFcmTokenDto })
  @ApiResponse({
    status: 200,
    description: 'FCM token created/updated successfully',
    type: MemberFcmToken,
  })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async createFcmToken(
    @Param('id', ParseIntPipe) memberId: number,
    @Body() createFcmTokenDto: CreateFcmTokenDto,
    @Res({ passthrough: true }) response: Response,
    @Req() request: Request,
  ): Promise<MemberFcmToken> {
    const user = request.user;
    if (user.id !== Number(memberId)) {
      throw new UnauthorizedException();
    }
    const token = await this.membersService.createFcmToken(
      memberId,
      createFcmTokenDto,
    );
    response.locals.message = 'FCM token created/updated successfully';
    return token;
  }
}
