import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  Index,
} from 'typeorm';

@Entity('stored_events')
@Index(['eventName', 'timestamp'])
@Index(['correlationId'])
@Index(['processedAt'])
export class StoredEvent {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'event_name' })
  eventName: string;

  @Column({ name: 'event_data', type: 'jsonb' })
  eventData: any;

  @Column({ name: 'metadata', type: 'jsonb', nullable: true })
  metadata: Record<string, any>;

  @Column({ name: 'correlation_id', nullable: true })
  correlationId: string;

  @Column({ name: 'causation_id', nullable: true })
  causationId: string;

  @Column({ name: 'aggregate_id', nullable: true })
  aggregateId: string;

  @Column({ name: 'aggregate_type', nullable: true })
  aggregateType: string;

  @Column({ name: 'version', default: 1 })
  version: number;

  @Column({ name: 'timestamp' })
  timestamp: Date;

  @Column({ name: 'processed_at', nullable: true })
  processedAt: Date;

  @Column({ name: 'processed_by', nullable: true })
  processedBy: string;

  @Column({ name: 'failed_at', nullable: true })
  failedAt: Date;

  @Column({ name: 'error_message', nullable: true })
  errorMessage: string;

  @Column({ name: 'retry_count', default: 0 })
  retryCount: number;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;
}
