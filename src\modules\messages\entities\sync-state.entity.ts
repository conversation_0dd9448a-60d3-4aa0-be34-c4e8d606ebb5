import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToOne,
} from 'typeorm';
import { MessageThread } from './message-thread.entity';

@Entity('sync_state')
export class SyncState {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'bigint', default: 0 })
  lastSyncedSeq: number; // last successfully synced message/group seq

  @Column({ type: 'bigint', default: 0 })
  lastAckedSeq: number; // last message user acknowledged

  @Column({ default: false })
  hasPending: boolean; // quick check if thread needs sync

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  @OneToOne(() => MessageThread, (thread) => thread.syncState)
  thread: MessageThread;
}
