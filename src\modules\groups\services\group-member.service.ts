import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, QueryRunner, Repository } from 'typeorm';
import { GroupMember } from '../entities/group-member.entity';
import { Group } from '../entities/group.entity';
import { OrgMember } from '../../members/entities/org-member.entity';
import { GroupEncryptionKey } from '../entities/group-encryption-keys.entity';
import { GroupLogService } from '../../audit/services/group.log.service';
import {
  GroupChangeType,
  GroupLogAction,
} from '../../audit/enums/group-log-action.enum';

@Injectable()
export class GroupMembersService {
  constructor(
    @InjectRepository(GroupMember)
    private readonly groupMemberRepo: Repository<GroupMember>,
    @InjectRepository(Group)
    private readonly groupRepo: Repository<Group>,
    @InjectRepository(OrgMember)
    private readonly orgMemberRepo: Repository<OrgMember>,
    @InjectRepository(GroupEncryptionKey)
    private readonly groupEncryptionKeyRepository: Repository<GroupEncryptionKey>,
    private readonly groupLogService: GroupLogService,
  ) {}

  async getAssociatedMemberIds(currentMemberId: number): Promise<number[]> {
    // Get group IDs where current member is active and hasn't left
    const groupIds = await this.groupMemberRepo
      .createQueryBuilder('gm')
      .select('gm.groupId', 'groupId')
      .where('gm.memberId = :id', { id: currentMemberId })
      .andWhere('gm.isActive = :active', { active: true })
      .andWhere('gm.leftAt IS NULL')
      .getRawMany();

    if (groupIds.length === 0) {
      return [];
    }

    // Get members in those groups who are active, haven't left, and exclude current member
    const members = await this.groupMemberRepo
      .createQueryBuilder('gm')
      .select('gm.memberId')
      .where('gm.groupId IN (:...ids)', { ids: groupIds.map((g) => g.groupId) })
      .andWhere('gm.memberId != :id', { id: currentMemberId })
      .andWhere('gm.isActive = :active', { active: true })
      .andWhere('gm.leftAt IS NULL')
      .getMany();

    return [...new Set(members.map((m) => m.memberId))];
  }

  async findOneFile(id: number) {
    return this.groupMemberRepo.findOne({
      where: { id },
      relations: ['file'],
    });
  }

  // async getGroupMessagesWithKey(groupId: number, memberId: number) {
  //   // Fetch group with members
  //   const group = await this.groupRepo.findOne({
  //     where: { id: groupId },
  //     relations: ['members', 'members.member'],
  //   });
  //   if (!group) throw new NotFoundException('Group not found');

  //   const groupMembership = await this.groupMemberRepo.findOne({
  //     where: { groupId, memberId, isActive: true },
  //   });

  //   if (!groupMembership) throw new Error('Member not part of this group');

  //   const joinedAt = groupMembership.joinedAt;

  //   // Fetch messages as before
  //   const messages = await this.messagesService.getMessagesInGroup(
  //     groupId,
  //     joinedAt,
  //   );

  //   // Fetch the encrypted group key for this member and currentKeyVersion
  //   const encryptionKeyEntry = await this.groupEncryptionKeyRepository.findOne({
  //     where: {
  //       groupId,
  //       memberId,
  //       keyVersion: group.currentKeyVersion,
  //       isActive: true,
  //     },
  //   });

  //   const encryptedGroupKey = encryptionKeyEntry?.encryptedGroupKey || null;

  //   // Prepare members list as before
  //   const groupMembers = group.members
  //     .filter((m) => m.isActive)
  //     .map((gm) => ({
  //       id: gm.member.id,
  //       name: gm.member.name,
  //       imageUrl: gm.member.imageUrl,
  //       joinedAt: gm.joinedAt?.toISOString(),
  //       isMute: gm.isMute,
  //     }));

  //   const groupDetails = {
  //     groupId: group.id,
  //     groupName: group.name,
  //     imageUrl: group.imageUrl,
  //     currentKeyVersion: group.currentKeyVersion,
  //     encryptedGroupKey,
  //     createdAt: group.createdAt,
  //     lastUpdated: group.updatedAt,
  //     groupMembers,
  //     messages: messages.map((msg) => {
  //       return {
  //         id: msg.id,
  //         groupId: msg.groupId,
  //         groupKeyVersion: msg.groupKeyVersion,
  //         from: {
  //           id: msg.sender?.id,
  //           name: msg.sender?.name,
  //           imageUrl: msg.sender?.imageUrl,
  //         },
  //         date: msg.createdAt?.toISOString(),
  //         msg: msg?.encryptedContent,
  //         nonce: (msg as any)?.nonce,
  //         file: msg?.file
  //           ? {
  //               id: msg.file.id,
  //               fileUrl: msg.file.fileUrl,
  //               fileName: msg.file.filename,
  //               type: msg.file.fileType,
  //               size: msg.file.fileSize,
  //               metaData: msg.file.extraMetadata,
  //             }
  //           : null,
  //         repliedMessage: (msg as any).replyTo
  //           ? {
  //               id: (msg as any).replyTo.id,
  //               text: (msg as any).replyTo.encryptedContent,
  //               nonce: (msg as any).replyTo?.nonce,
  //               replyGroupKeyVersion: (msg as any).replyTo.groupKeyVersion,
  //               createdAt: (msg as any).replyTo.sentAt?.toISOString(),
  //               user: {
  //                 id: (msg as any).replyTo.sender?.id,
  //                 name: (msg as any).replyTo.sender?.name,
  //                 imageUrl: (msg as any).replyTo.sender?.imageUrl,
  //               },
  //             }
  //           : null,
  //         readBy: (msg as any)?.reads
  //           ? (msg as any).reads.map((reader: any) => ({
  //               id: reader.readerId,
  //             }))
  //           : [],
  //       };
  //     }),
  //   };

  //   return groupDetails;
  // }

  async getAllGroupEncryptionKeysForMember(memberId: number) {
    const encryptionKeys = await this.groupEncryptionKeyRepository.find({
      where: { memberId },
      order: { groupId: 'ASC', keyVersion: 'ASC' },
    });

    // Group keys by groupId into an array of objects
    const grouped = new Map<number, any[]>();

    for (const key of encryptionKeys) {
      if (!grouped.has(key.groupId)) {
        grouped.set(key.groupId, []);
      }

      grouped.get(key.groupId)!.push({
        keyVersion: key.keyVersion,
        encryptedGroupKey: key.encryptedGroupKey,
        isActive: key.isActive,
      });
    }

    // Transform Map into array
    const result = Array.from(grouped.entries()).map(([groupId, keys]) => ({
      groupId,
      encryptionKeys: keys,
    }));

    return result;
  }

  async calculateMemberChanges(
    groupId: number,
    targetMemberIds: number[],
    queryRunner: QueryRunner,
  ): Promise<{
    membersToAdd: number[];
    membersToRemove: number[];
    existingMemberIds: number[];
  }> {
    const existingGroupMembers = await queryRunner.manager.find(GroupMember, {
      where: { groupId, isActive: true },
    });

    const existingMemberIds = existingGroupMembers.map((m) => m.memberId);
    const membersToAdd = targetMemberIds.filter(
      (id) => !existingMemberIds.includes(id),
    );
    const membersToRemove = existingMemberIds.filter(
      (id) => !targetMemberIds.includes(id),
    );

    return {
      membersToAdd,
      membersToRemove,
      existingMemberIds,
    };
  }

  async removeMembers(
    groupId: number,
    membersToRemove: number[],
    queryRunner: QueryRunner,
  ): Promise<void> {
    for (const memberId of membersToRemove) {
      await queryRunner.manager.update(
        GroupMember,
        { groupId, memberId, isActive: true },
        { isActive: false, leftAt: new Date() },
      );

      await this.groupLogService.logChange(
        groupId,
        GroupChangeType.MEMBER,
        GroupLogAction.LEAVE,
        { memberId },
      );
    }
  }

  async addMembers(
    groupId: number,
    membersToAdd: number[],
    queryRunner: QueryRunner,
  ): Promise<{
    addedMembers: any[];
    newMemberDetails: any[];
  }> {
    const addedMembers: any[] = [];
    const newMemberDetails: any[] = [];

    for (const memberId of membersToAdd) {
      const member = await queryRunner.manager.findOne(OrgMember, {
        where: { id: memberId },
        select: ['id', 'email', 'name', 'phoneNo', 'isVerified'],
      });

      if (!member) continue;

      const newMembership = await queryRunner.manager.save(GroupMember, {
        groupId,
        memberId,
        joinedAt: new Date(),
      });

      addedMembers.push(newMembership);
      newMemberDetails.push({
        ...member,
        isVerified: member.isVerified,
      });

      await this.groupLogService.logChange(
        groupId,
        GroupChangeType.MEMBER,
        GroupLogAction.JOIN,
        { memberId },
      );
    }

    return { addedMembers, newMemberDetails };
  }

  async getGroupMemberDetails(groupId: number) {
    const group = await this.groupRepo.findOne({
      where: { id: groupId },
      relations: ['members', 'members.member'],
    });
    if (!group) throw new Error('Group not found');

    const groupMembers = group.members
      .filter((m) => m.isActive)
      .map((gm) => ({
        id: gm.member.id,
        name: gm.member.name,
        imageUrl: gm.member.imageUrl,
        joinedAt: gm.joinedAt,
        isMute: gm.isMute,
        groupMemberId: gm.id,
        phoneNo: gm.member.phoneNo || null,
      }));

    return {
      groupId: group.id,
      groupName: group.name,
      groupImageUrl: group.imageUrl,
      members: groupMembers,
    };
  }

  async findGroupMember(groupId: number, memberId: number) {
    return this.groupMemberRepo.findOne({
      where: { groupId, memberId, isActive: true },
      relations: ['group', 'member'], // optional: add related entities
    });
  }

  async getGroupMembers(groupId: number) {
    const groupMembers = await this.groupMemberRepo.find({
      where: { groupId, isActive: true },
      relations: ['member'],
    });

    return groupMembers.map((gm) => ({
      id: gm.member.id,
      name: gm.member.name,
      imageUrl: gm.member.imageUrl,
      joinedAt: gm.joinedAt?.toISOString(),
      isMute: gm.isMute,
    }));
  }

  async deltaSyncGroup(sequence: number, groupId: number, memberId: number) {
    // Fetch latest group entity
    const group = await this.groupRepo.findOne({
      where: { id: groupId },
      relations: ['members', 'members.member', 'encryptionKeys'],
    });
    if (!group) throw new NotFoundException('Group not found');

    // Fetch changes
    const [memberChanges, keyChanges, profileChanges] = await Promise.all([
      this.groupLogService.getChangesFromSeq(
        groupId,
        GroupChangeType.MEMBER,
        sequence,
      ),
      this.groupLogService.getChangesFromSeq(
        groupId,
        GroupChangeType.KEY,
        sequence,
        memberId,
      ),
      this.groupLogService.getProfileChangesFromSeq(memberId, sequence),
    ]);

    // Collect updated members
    const memberIds = [
      ...new Set(memberChanges.map((c) => c.memberId).filter(Boolean)),
      ...profileChanges.map((c) => c.memberId).filter(Boolean),
    ];

    const membersMap = new Map<number, any>();
    if (memberIds.length > 0) {
      const members = await this.orgMemberRepo.find({
        where: { id: In(memberIds) },
        select: ['id', 'name', 'imageUrl', 'phoneNo'],
      });
      for (const m of members) membersMap.set(m.id, m);
    }
    const members = Array.from(membersMap.values());

    // Key mapping
    const memberKeys = group.encryptionKeys
      .filter((k) => k.memberId === memberId)
      .reduce(
        (acc, key) => {
          acc[key.keyVersion] = key.encryptedGroupKey;
          return acc;
        },
        {} as Record<number, string>,
      );

    const changes: Array<any> = [];

    // Member changes
    memberChanges
      .sort((a, b) => a.seq - b.seq)
      .forEach((c) => {
        const type = c.memberId === memberId ? 'myMembership' : 'member';
        changes.push({
          seq: c.seq,
          type,
          timestamp: c.createdAt || new Date().toISOString(),
          payload:
            type === 'member'
              ? { memberId: c.memberId, action: c.action }
              : {
                  action: c.action,
                  timestamp: c.createdAt || new Date().toISOString(),
                },
        });
      });

    // Key changes
    keyChanges
      .sort((a, b) => a.seq - b.seq)
      .forEach((c) => {
        changes.push({
          seq: c.seq,
          type: 'myKey',
          timestamp: c.createdAt || new Date().toISOString(),
          payload: {
            keyVersion: Number(c.newValue),
            encryptedGroupKey: memberKeys[Number(c.newValue)] || null,
          },
        });
      });

    // Profile changes
    profileChanges
      .sort((a, b) => a.seq - b.seq)
      .forEach((c) => {
        changes.push({
          seq: c.seq,
          type: 'memberProfile',
          timestamp: c.createdAt || new Date().toISOString(),
          payload: {
            memberId: c.memberId,
            changedField: c.changedField,
            oldValue: c.oldValue,
            newValue: c.newValue,
          },
        });
      });

    const lastSeq = Math.max(sequence, ...changes.map((c) => c.seq));

    const groupMeta = {
      id: group.id,
      orgId: group.orgId,
      name: group.name,
      description: group.description,
      isActive: group.isActive,
      imageUrl: group.imageUrl,
      createdBy: group.createdBy,
      createdAt: group.createdAt,
      updatedAt: group.updatedAt,
      deletedAt: group.deletedAt,
      deletedBy: group.deletedBy,
      currentKeyVersion: group.currentKeyVersion,
    };

    return {
      groupId: group.id,
      groupMeta,
      members,
      changes: changes.sort((a, b) => a.seq - b.seq),
      lastSeq,
    };
  }
  async findActiveMembership(
    memberId: number,
    groupId: number,
  ): Promise<GroupMember | null> {
    return await this.groupMemberRepo.findOne({
      where: {
        memberId,
        groupId,
        isActive: true,
      },
    });
  }

  async getActiveMembersWithDetails(groupId: number) {
    return this.groupMemberRepo.find({
      where: { groupId, isActive: true },
      relations: ['member'],
    });
  }

  async getMembershipStatus(
    groupId: number,
    memberId: number,
  ): Promise<'joined' | 'left' | 'not found'> {
    const record = await this.groupMemberRepo.findOne({
      where: {
        groupId,
        memberId,
      },
    });

    if (!record) {
      return 'not found';
    }

    return record.leftAt ? 'left' : 'joined';
  }
}
