import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

export class AdminResponseDto {
  @ApiProperty({ description: 'User ID' })
  @IsNumber()
  id: number;

  @ApiProperty({ description: 'Username of the admin' })
  @IsString()
  username: string;

  @ApiProperty({ description: 'Email of the admin' })
  @IsString()
  email: string;

  @ApiProperty({
    description: 'Type of admin',
    enum: ['product_admin', 'org_admin'],
  })
  @IsEnum(['product_admin', 'org_admin'])
  type: 'product_admin' | 'org_admin';

  @ApiPropertyOptional({ description: 'Organization ID if applicable' })
  @IsOptional()
  @IsNumber()
  orgId?: number;

  @ApiPropertyOptional({ description: 'Organization Name if applicable' })
  @IsOptional()
  @IsString()
  orgName?: string;

  @ApiProperty({ description: 'Role ID of the admin' })
  @IsNumber()
  roleId: number;

  @ApiPropertyOptional({ description: 'Profile image URL of the admin' })
  @IsOptional()
  @IsString()
  imageUrl?: string;

  @ApiPropertyOptional({ description: 'Encrypted Admin Secret Key' })
  @IsOptional()
  @IsString()
  encryptedAdminSecretKey?: string;

  @ApiPropertyOptional({ description: 'Admin Secret Key Nonce' })
  @IsOptional()
  @IsString()
  adminSecretKeyNonce?: string;

  @ApiPropertyOptional({ description: 'Admin Secret Key Salt' })
  @IsOptional()
  @IsString()
  adminSecretKeySalt?: string;
}

// ==================== LoginResponseDto ====================
export class LoginResponseDto {
  @ApiProperty({ description: 'JWT access token for authentication' })
  @IsString()
  accessToken: string;

  @ApiProperty({
    description: 'JWT refresh token for getting new access tokens',
  })
  @IsString()
  refreshToken: string;
}
