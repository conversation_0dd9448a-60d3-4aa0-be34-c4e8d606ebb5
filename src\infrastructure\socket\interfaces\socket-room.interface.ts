export interface ISocketRoomService {
  // Member rooms
  broadcastToMember(memberId: number, event: string, data: any): void;

  // Group rooms
  emitToGroup(groupId: number, event: string, data: any): void;

  // Private chat rooms (1-on-1)
  emitToPrivateChat(
    member1Id: number,
    member2Id: number,
    event: string,
    data: any,
  ): void;

  // Organization rooms (all members of an organization)
  emitToOrganization(organizationId: number, event: string, data: any): void;

  // Statistics
  getConnectedClientsCount(): Promise<number>;
  getRoomClientsCount(
    roomType: string,
    ...identifiers: (string | number)[]
  ): Promise<number>;
}
