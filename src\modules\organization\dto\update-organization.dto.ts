import { PartialType } from '@nestjs/mapped-types';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { CreateOrganizationDto } from './create-organization.dto';
import { OrganizationStatus } from '../entities/organization.entity';
import { ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateOrganizationDto extends PartialType(CreateOrganizationDto) {
  @ApiPropertyOptional({
    description: 'Updated name of the organization',
    example: 'OpenAI Research Group',
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({
    description: 'Updated location of the organization',
    example: 'New York, NY',
  })
  @IsOptional()
  @IsString()
  location?: string;

  @ApiPropertyOptional({
    description: 'Updated phone number',
    example: '+14155550000',
  })
  @IsOptional()
  @IsString()
  phoneNo?: string;

  @ApiPropertyOptional({
    description: 'Updated admin email for the organization',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  adminEmail?: string;

  @ApiPropertyOptional({
    description: 'Updated status of the organization',
    enum: OrganizationStatus,
    example: OrganizationStatus.INACTIVE,
  })
  @IsOptional()
  @IsEnum(OrganizationStatus)
  status?: OrganizationStatus;

  @ApiPropertyOptional({
    description: 'Updated file URL for organization logo',
    example: 'https://example.com/new-logo.png',
  })
  @IsOptional()
  @IsString()
  fileUrl?: string;
}
