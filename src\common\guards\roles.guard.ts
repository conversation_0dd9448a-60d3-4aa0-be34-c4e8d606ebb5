import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { Request } from 'express';
import { UserType } from '../types/user-type';
import { AuthenticatedUser } from '../types/authenticate-user.types';

@Injectable()
export class RolesGuard implements CanActivate {
  constructor(private reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const allowedUserTypes = this.reflector.getAllAndOverride<UserType[]>(
      'allowedUserTypes',
      [context.getHandler(), context.getClass()],
    );

    if (!allowedUserTypes) {
      return true; // No role restriction
    }

    const request = context.switchToHttp().getRequest<Request>();
    const user = request.user as AuthenticatedUser;

    if (!user) {
      throw new UnauthorizedException('User not authenticated');
    }

    const hasAllowedRole = allowedUserTypes.includes(user.type);

    if (!hasAllowedRole) {
      throw new UnauthorizedException('Insufficient permissions');
    }

    return true;
  }
}
