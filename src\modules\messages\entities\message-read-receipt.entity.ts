import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  ManyTo<PERSON>ne,
  <PERSON>in<PERSON><PERSON>um<PERSON>,
} from 'typeorm';
import { GroupMessage } from './group-message.entity';
import { OrgMember } from '../../members/entities/org-member.entity';

@Entity('group_message_reads')
export class GroupMessageRead {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'message_id' })
  messageId: number;

  @Column({ name: 'reader_id' })
  readerId: number;

  @Column({ name: 'read_at' })
  readAt: Date;

  // Relations
  @ManyToOne(() => GroupMessage, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'message_id' })
  message: GroupMessage;

  @ManyToOne(() => OrgMember, (orgMember) => orgMember.readMessages)
  @JoinColumn({ name: 'reader_id' })
  reader: OrgMember;
}
