import { Controller, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { register } from 'prom-client';
import { Public } from 'src/common/decorators/public.decorator';

@Controller('metrics')
export class MonitoringController {
  @Public()
  @Get()
  async getMetrics(@Res() res: Response) {
    try {
      const metrics = await register.metrics();
      res.set('Content-Type', register.contentType);
      res.send(metrics);
    } catch (error) {
      res.status(500).send(`Error getting metrics: ${error.message}`);
    }
  }
}
