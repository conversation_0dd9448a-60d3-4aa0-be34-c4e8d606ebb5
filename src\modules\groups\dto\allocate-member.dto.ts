import {
  ArrayNotEmpty,
  ArrayUnique,
  IsArray,
  IsNotEmpty,
  IsNumber,
  IsString,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class AllocateMemberDto {
  @ApiProperty({ description: 'ID of the group' })
  @IsNotEmpty()
  @IsNumber()
  groupId: number;

  @ApiProperty({
    description: 'Array of member IDs to allocate',
    type: [Number],
  })
  @IsNotEmpty()
  @IsArray()
  @ArrayNotEmpty()
  @ArrayUnique()
  @IsNumber({}, { each: true })
  memberIds: number[];

  @ApiProperty({ description: 'Admin secret key for authorization' })
  @IsNotEmpty()
  @IsString()
  adminSecretKey: string;
}
