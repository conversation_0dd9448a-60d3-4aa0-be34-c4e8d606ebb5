import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventBusService } from '../events/event-bus.service';
import {
  CircuitBreakerClosedEvent,
  CircuitBreakerHalfOpenEvent,
  CircuitBreakerOpenedEvent,
} from 'src/common/events/circuit-breaker.events';

import CircuitBreaker = require('opossum');
@Injectable()
export class CircuitBreakerService {
  private readonly logger = new Logger(CircuitBreakerService.name);

  private breakers: Map<string, CircuitBreaker> = new Map();

  constructor(
    private configService: ConfigService,
    private eventBus: EventBusService,
  ) {}

  private createBreaker(name: string, options: CircuitBreaker.Options) {
    const breaker = new CircuitBreaker(async (action: () => Promise<any>) => {
      return action();
    }, options);

    breaker.on('open', () => {
      this.logger.warn(`[CircuitBreaker] ${name} breaker opened`);
      this.eventBus.publish(new CircuitBreakerOpenedEvent({ service: name }));
    });

    breaker.on('halfOpen', () => {
      this.logger.log(`[CircuitBreaker] ${name} breaker half-open`);
      this.eventBus.publish(new CircuitBreakerHalfOpenEvent({ service: name }));
    });

    breaker.on('close', () => {
      this.logger.log(`[CircuitBreaker] ${name} breaker closed`);
      this.eventBus.publish(new CircuitBreakerClosedEvent({ service: name }));
    });

    return breaker;
  }

  getBreaker(name: string): CircuitBreaker {
    if (this.breakers.has(name)) {
      return this.breakers.get(name)!;
    }

    // Load config for domain, fallback to global defaults
    const domainConfig = this.configService.get(`circuitBreaker.${name}`) || {};
    const globalConfig = this.configService.get('circuitBreaker.global') || {};

    const options: CircuitBreaker.Options = {
      errorThresholdPercentage:
        domainConfig.failureThreshold ?? globalConfig.defaultFailureThreshold,
      resetTimeout:
        domainConfig.resetTimeout ?? globalConfig.defaultResetTimeout,
      rollingCountTimeout:
        domainConfig.monitoringPeriod ?? globalConfig.defaultMonitoringPeriod,
      // Customize more options here if needed
    };

    const breaker = this.createBreaker(name, options);
    this.breakers.set(name, breaker);
    return breaker;
  }

  async executeWithBreaker<T>(
    domain: string,
    action: () => Promise<T>,
  ): Promise<T> {
    const breaker = this.getBreaker(domain);
    return breaker.fire(action) as Promise<T>;
  }
}
