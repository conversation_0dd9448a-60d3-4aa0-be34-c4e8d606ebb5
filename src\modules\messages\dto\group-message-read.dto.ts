import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsInt, IsNotEmpty } from 'class-validator';

export class GroupMessageReadDto {
  @ApiProperty({ example: 1001, description: 'Message ID' })
  @IsNotEmpty()
  @IsInt()
  messageId: number;

  @ApiProperty({ example: 2, description: 'Reader user ID' })
  @IsNotEmpty()
  @IsInt()
  readerId: number;

  @ApiProperty({
    example: '2025-08-26T12:30:00.000Z',
    description: 'When the message was read',
  })
  @IsNotEmpty()
  @IsDate()
  @Type(() => Date)
  readAt: Date;
}
