export class SendMessageRequestedEvent {
  constructor(
    public payload: {
      chatType: string;
      chatId: string;
      senderId: number;
      content: string;
      messageType: string;
      attachments: any[];
      replyToMessageId?: string;
      metadata?: any;
      socketId: string;
    },
  ) {}
}

export class MessageReadRequestedEvent {
  constructor(
    public payload: {
      chatType: string;
      chatId: string;
      readerId: number;
      messageIds: number[];
      readAt: Date;
    },
  ) {}
}

export class DeleteMessageForMeRequestedEvent {
  constructor(
    public payload: {
      chatType: string;
      chatId: string;
      deleterId: number;
      messageId: number;
      deletedAt: Date;
    },
  ) {}
}

export class DeleteMessagesForEveryoneRequestedEvent {
  constructor(
    public payload: {
      chatType: string;
      chatId: string;
      deleterId: number;
      messageIds: number[];
      deletedAt: Date;
    },
  ) {}
}

export class UserTypingEvent {
  constructor(
    public payload: {
      chatType: string;
      chatId: string;
      memberId: number;
      isTyping: boolean;
      duration?: number;
    },
  ) {}
}

export class MessageReadEvent {
  constructor(
    public payload: {
      chatType: string;
      chatId: string;
      readInfo: any;
    },
  ) {}
}

export class MessageDeletedEvent {
  constructor(
    public payload: {
      chatType: string;
      chatId: string;
      deletionInfo: any;
    },
  ) {}
}

