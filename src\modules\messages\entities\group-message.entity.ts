import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  OneToMany,
} from 'typeorm';
import { Group } from '../../groups/entities/group.entity';
import { OrgMember } from '../../members/entities/org-member.entity';
import { GroupMessageRead } from './message-read-receipt.entity';
import { MediaFile } from '../../media/entities/media-file.entity';

@Entity('group_messages')
export class GroupMessage {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ type: 'bigint' })
  seq: number; // Monotonic sequence for delta sync

  @Column({ name: 'group_id' })
  groupId: number;

  @Column({ name: 'sender_id' })
  senderId: number;

  @Column({ name: 'encrypted_content', type: 'text', nullable: true })
  encryptedContent: string;

  @Column({ name: 'media_caption', type: 'text', nullable: true })
  caption: string;

  @Column({ name: 'nonce', nullable: true })
  nonce: string;

  @Column({ name: 'group_key_version' })
  groupKeyVersion: number;

  @Column({ name: 'sent_at' })
  sentAt: Date;

  @Column({ name: 'encrypted_meta_data', type: 'jsonb', nullable: true })
  encryptedMetaData: Record<string, any>;

  @Column({ name: 'is_deleted', default: false })
  isDeleted: boolean;

  @Column({ name: 'reply_to_message_id', nullable: true })
  replyToMessageId: number;

  @Column({ name: 'file_id', nullable: true })
  fileId: number;

  @Column({ name: 'delivered_count', default: 0 })
  deliveredCount: number;

  @Column({ name: 'read_count', default: 0 })
  readCount: number;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  // Relations
  @ManyToOne(() => Group)
  @JoinColumn({ name: 'group_id' })
  group: Group;

  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'sender_id' })
  sender: OrgMember;

  @ManyToOne(() => GroupMessage, { nullable: true })
  @JoinColumn({ name: 'reply_to_message_id' })
  replyTo: GroupMessage;

  @OneToMany(() => GroupMessageRead, (read) => read.message)
  reads: GroupMessageRead[];

  @ManyToOne(() => MediaFile, (file) => file.groupMessages, { nullable: true })
  @JoinColumn({ name: 'file_id' })
  file: MediaFile;
}
