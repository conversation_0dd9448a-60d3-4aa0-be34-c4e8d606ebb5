// src/modules/security/services/identity-key.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EntityManager, Repository } from 'typeorm';
import {
  EncryptedPrivateKey,
  IdentityKeyEntity,
} from '../entities/identity-key.entity';

@Injectable()
export class IdentityKeyService {
  constructor(
    @InjectRepository(IdentityKeyEntity)
    private readonly identityKeyRepo: Repository<IdentityKeyEntity>,
  ) {}

  /**
   * Creates an identity key record for a member.
   * @param memberId The member's ID
   * @param publicKey The member's public identity key
   * @param encryptedPrivateKey The member's encrypted private key
   */
  async createIdentityKey(
    memberId: number,
    publicKey: string,
    encryptedPrivateKey: EncryptedPrivateKey,
  ): Promise<IdentityKeyEntity> {
    const identityKey = this.identityKeyRepo.create({
      memberId,
      publicKey,
      encryptedPrivateKey,
    });

    return await this.identityKeyRepo.save(identityKey);
  }

  /**
   * Retrieves identity key for a member
   */
  async getIdentityKey(memberId: number): Promise<IdentityKeyEntity | null> {
    return this.identityKeyRepo.findOne({ where: { memberId } });
  }

  /**
   * Deletes identity key for a member
   */
  async deleteIdentityKey(memberId: number): Promise<void> {
    await this.identityKeyRepo.delete({ memberId });
  }

  async updateIdentityKey(
    memberId: number,
    publicKey: string,
    encryptedPrivateKey: EncryptedPrivateKey,
    manager?: EntityManager,
  ): Promise<IdentityKeyEntity> {
    const repo = manager
      ? manager.getRepository(IdentityKeyEntity)
      : this.identityKeyRepo;

    // Update inside transaction if manager is passed
    await repo.update({ memberId }, { publicKey, encryptedPrivateKey });

    const updated = await repo.findOne({ where: { memberId } });
    if (!updated) {
      throw new NotFoundException(
        `IdentityKeyEntity not found for memberId: ${memberId}`,
      );
    }
    return updated;
  }
}
