import { Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>al, IsArray } from 'class-validator';

export class SocketSendPrivateMessageDto {
  @IsString()
  chatType: string;

  @IsString()
  chatId: string;

  @IsNumber()
  senderId: number;

  @IsNumber()
  receiverId: number;

  @IsString()
  content: string;

  @IsString()
  nonce: string;

  @IsOptional()
  metadata?: any;

  @IsOptional()
  @IsString()
  replyToMessageId?: string;

  @IsOptional()
  @IsArray()
  attachments?: any[];

  @IsString()
  messageType: string = 'text';
}
