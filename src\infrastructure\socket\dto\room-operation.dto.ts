import { IsEnum, IsArray, IsNotEmpty } from 'class-validator';

export class JoinRoomDto {
  @IsEnum(['member', 'group', 'private', 'organization'])
  type: 'member' | 'group' | 'private' | 'organization';

  @IsArray()
  @IsNotEmpty()
  identifiers: (string | number)[];
}

export class LeaveRoomDto {
  @IsEnum(['member', 'group', 'private', 'organization'])
  type: 'member' | 'group' | 'private' | 'organization';

  @IsArray()
  @IsNotEmpty()
  identifiers: (string | number)[];
}

export class JoinMultipleRoomsDto {
  @IsArray()
  @IsNotEmpty()
  rooms: JoinRoomDto[];
}
