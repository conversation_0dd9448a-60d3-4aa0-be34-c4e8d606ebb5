import {
  <PERSON>,
  Get,
  Post,
  Param,
  Query,
  Req,
  Res,
  ParseIntPipe,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { MediaStreamingService } from '../services/media-streaming.service';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';

@ApiTags('Media Streaming')
@ApiBearerAuth()
@Controller('media/stream')
export class MediaStreamingController {
  constructor(private readonly mediaStreamingService: MediaStreamingService) {}

  @Get('video/:fileId')
  @ApiOperation({ summary: 'Stream video file with range support' })
  @ApiParam({ name: 'fileId', description: 'Video file ID' })
  @ApiResponse({ status: 200, description: 'Video stream started' })
  async streamVideo(
    @Param('fileId') fileId: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    const range = req.headers.range;
    const videoStream = await this.mediaStreamingService.streamVideo(
      fileId,
      range,
    );
    return videoStream.pipe(res);
  }

  @Get('audio/:fileId')
  @ApiOperation({ summary: 'Stream audio file' })
  @ApiParam({ name: 'fileId', description: 'Audio file ID' })
  @ApiResponse({ status: 200, description: 'Audio stream started' })
  async streamAudio(
    @Param('fileId') fileId: string,
    @Req() req: Request,
    @Res() res: Response,
  ) {
    const range = req.headers.range;
    const audioStream = await this.mediaStreamingService.streamAudio(
      fileId,
      range,
    );
    return audioStream.pipe(res);
  }

  @Get('hls/:fileId/playlist.m3u8')
  @ApiOperation({ summary: 'Get HLS playlist for adaptive streaming' })
  @ApiParam({ name: 'fileId', description: 'Video file ID' })
  @ApiQuery({ name: 'quality', required: false, type: String })
  async getHLSPlaylist(
    @Param('fileId') fileId: string,
    @Query('quality') quality?: string,
  ) {
    return await this.mediaStreamingService.generateHLSPlaylist(
      fileId,
      quality,
    );
  }

  @Get('hls/:fileId/:segmentId')
  @ApiOperation({ summary: 'Get HLS segment' })
  @ApiParam({ name: 'fileId', description: 'Video file ID' })
  @ApiParam({ name: 'segmentId', description: 'Segment ID' })
  async getHLSSegment(
    @Param('fileId') fileId: string,
    @Param('segmentId') segmentId: string,
    @Res() res: Response,
  ) {
    const segmentStream = await this.mediaStreamingService.getHLSSegment(
      fileId,
      segmentId,
    );
    return segmentStream.pipe(res);
  }

  @Post('live/start')
  @ApiOperation({ summary: 'Start live streaming session' })
  @ApiQuery({ name: 'userId', type: Number })
  @ApiQuery({ name: 'quality', required: false, type: String })
  async startLiveStream(
    @Query('userId', ParseIntPipe) userId: number,
    @Query('quality') quality: string = 'medium',
  ) {
    return await this.mediaStreamingService.startLiveStream(userId, quality);
  }

  @Post('live/:streamId/stop')
  @ApiOperation({ summary: 'Stop live streaming session' })
  @ApiParam({ name: 'streamId', description: 'Live stream ID' })
  async stopLiveStream(@Param('streamId') streamId: string) {
    await this.mediaStreamingService.stopLiveStream(streamId);
    return { message: 'Live stream stopped successfully' };
  }

  @Get(':fileId/stats')
  @ApiOperation({ summary: 'Get streaming statistics' })
  @ApiParam({ name: 'fileId', description: 'File ID' })
  async getStreamingStats(@Param('fileId') fileId: string) {
    return await this.mediaStreamingService.getStreamingStats(fileId);
  }

  @Get('dash/:fileId/manifest.mpd')
  @ApiOperation({ summary: 'Generate DASH manifest for adaptive streaming' })
  @ApiParam({ name: 'fileId', description: 'File ID' })
  async getDASHManifest(@Param('fileId') fileId: string) {
    return await this.mediaStreamingService.generateDASHManifest(fileId);
  }
}
