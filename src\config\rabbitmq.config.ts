import { registerAs } from '@nestjs/config';

export const rabbitmqConfig = registerAs('rabbitmq', () => {
  const user = process.env.RABBITMQ_USER || 'admin';
  const pass = process.env.RABBITMQ_PASSWORD || 'admin123';
  const host = process.env.RABBITMQ_HOST || 'rabbitmq';
  const port = process.env.RABBITMQ_PORT || '5672';

  const url = `amqp://${user}:${pass}@${host}:${port}`;

  return {
    url,
  };
});
