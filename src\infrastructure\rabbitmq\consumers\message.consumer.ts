import { RabbitSubscribe, AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Injectable, Logger } from '@nestjs/common';
import {
  MESSAGE_DELIVERY_QUEUE,
  MESSAGE_DELIVERY_ROUTING_KEY,
  MESSAGE_DLQ_EXCHANGE,
  MESSAGE_EXCHANGE,
} from 'src/common/constants';
import { EVENT_NAMES } from 'src/common/constants/event-names';
import {
  MessageDeliveryAttemptEvent,
  MessageDeliveryFailedEvent,
  MessageDeliverySuccessEvent,
} from 'src/common/events/message.events';
import { EventBusService } from 'src/infrastructure/events/event-bus.service';
import { NotificationService } from 'src/infrastructure/notification/services/notification.service';
import { MessageDeliveryTaskPayload } from '../producers/message.producer';

@Injectable()
export class MessageConsumer {
  private readonly logger = new Logger(MessageConsumer.name);

  constructor(
    private readonly notificationService: NotificationService,
    private readonly eventBus: EventBusService,
    private readonly amqpConnection: AmqpConnection,
  ) {}

  @RabbitSubscribe({
    exchange: MESSAGE_EXCHANGE,
    routingKey: MESSAGE_DELIVERY_ROUTING_KEY,
    queue: MESSAGE_DELIVERY_QUEUE,
    queueOptions: {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': MESSAGE_DLQ_EXCHANGE,
        'x-message-ttl': 30 * 60 * 1000,
        'x-max-retries': 3,
      },
    },
  })
  async handleMessageDelivery(message: MessageDeliveryTaskPayload) {
    const retryCount = message.retryCount || 0;

    try {
      // Emit delivery attempt event
      this.eventBus.publish(new MessageDeliveryAttemptEvent(message.messageId));

      this.logger.log(
        `Processing message delivery for message ${message.messageId}, retry: ${retryCount}`,
      );

      const { data, ...filteredMessageData } = message.notificationPayload;

      const groupId = data?.groupId;

      if (groupId) {
        // Broadcast to all members with acknowledgment
        await this.notificationService.broadcastGroupMessageWithValidation(
          groupId,
          message.memberNotificationInfo,
          EVENT_NAMES.NEW_GROUP_MESSAGE,
          data,
          message.notificationPayload,
        );
      }
      // Success
      this.eventBus.publish(
        new MessageDeliverySuccessEvent(
          message.messageId,
          message.memberNotificationInfo.map((m) => m.memberId).length,
        ),
      );
      this.logger.log(`Message ${message.messageId} delivered successfully`);
    } catch (error) {
      this.logger.error(
        `Delivery failed for message ${message.messageId}:`,
        error,
      );

      if (retryCount >= 3) {
        this.logger.warn(
          `Max retries reached, routing message ${message.messageId} to DLQ`,
        );

        // Send to DLQ manually
        await this.amqpConnection.publish(
          MESSAGE_DLQ_EXCHANGE,
          MESSAGE_DELIVERY_ROUTING_KEY,
          {
            ...message,
            failureReason: error.message || 'Max retries exceeded',
          },
        );

        this.eventBus.publish(
          new MessageDeliveryFailedEvent(
            message.messageId,
            error.message || 'DLQ',
          ),
        );

        return; // Acknowledge original queue
      }

      throw { ...error, retryCount: retryCount + 1 }; // Trigger retry
    }
  }
}
