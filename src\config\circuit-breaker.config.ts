import { registerAs } from '@nestjs/config';

interface CircuitBreakerConfig {
  notification: {
    failureThreshold: number;
    resetTimeout: number;
    monitoringPeriod: number;
    halfOpenMaxCalls: number;
  };
  message: {
    failureThreshold: number;
    resetTimeout: number;
    monitoringPeriod: number;
    halfOpenMaxCalls: number;
  };
  media: {
    failureThreshold: number;
    resetTimeout: number;
    monitoringPeriod: number;
    halfOpenMaxCalls: number;
  };
  global: {
    enabled: boolean;
    defaultFailureThreshold: number;
    defaultResetTimeout: number;
    defaultMonitoringPeriod: number;
  };
}

export const circuitBreakerConfig = registerAs(
  'circuitBreaker',
  (): CircuitBreakerConfig => ({
    notification: {
      failureThreshold: parseInt(
        process.env.CB_NOTIFICATION_FAILURE_THRESHOLD || '5',
        10,
      ),
      resetTimeout: parseInt(
        process.env.CB_NOTIFICATION_RESET_TIMEOUT || '60000',
        10,
      ), // 1 minute
      monitoringPeriod: parseInt(
        process.env.CB_NOTIFICATION_MONITORING_PERIOD || '60000',
        10,
      ), // 1 minute
      halfOpenMaxCalls: parseInt(
        process.env.CB_NOTIFICATION_HALF_OPEN_MAX_CALLS || '3',
        10,
      ),
    },
    message: {
      failureThreshold: parseInt(
        process.env.CB_MESSAGE_FAILURE_THRESHOLD || '10',
        10,
      ),
      resetTimeout: parseInt(
        process.env.CB_MESSAGE_RESET_TIMEOUT || '30000',
        10,
      ), // 30 seconds
      monitoringPeriod: parseInt(
        process.env.CB_MESSAGE_MONITORING_PERIOD || '60000',
        10,
      ), // 1 minute
      halfOpenMaxCalls: parseInt(
        process.env.CB_MESSAGE_HALF_OPEN_MAX_CALLS || '5',
        10,
      ),
    },
    media: {
      failureThreshold: parseInt(
        process.env.CB_MEDIA_FAILURE_THRESHOLD || '3',
        10,
      ),
      resetTimeout: parseInt(
        process.env.CB_MEDIA_RESET_TIMEOUT || '120000',
        10,
      ), // 2 minutes
      monitoringPeriod: parseInt(
        process.env.CB_MEDIA_MONITORING_PERIOD || '300000',
        10,
      ), // 5 minutes
      halfOpenMaxCalls: parseInt(
        process.env.CB_MEDIA_HALF_OPEN_MAX_CALLS || '2',
        10,
      ),
    },
    global: {
      enabled: process.env.CIRCUIT_BREAKER_ENABLED !== 'false', // Default true
      defaultFailureThreshold: parseInt(
        process.env.CB_DEFAULT_FAILURE_THRESHOLD || '5',
        10,
      ),
      defaultResetTimeout: parseInt(
        process.env.CB_DEFAULT_RESET_TIMEOUT || '60000',
        10,
      ), // 1 minute
      defaultMonitoringPeriod: parseInt(
        process.env.CB_DEFAULT_MONITORING_PERIOD || '60000',
        10,
      ), // 1 minute
    },
  }),
);
