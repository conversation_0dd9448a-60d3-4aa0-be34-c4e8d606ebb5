import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { OrgMember } from '../../members/entities/org-member.entity';
import { Group } from '../../groups/entities/group.entity';
import { User } from '../../users/entities/user.entity';

export enum OrganizationStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

@Entity('organizations')
export class Organization {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true })
  name: string;

  @Column()
  location: string;

  // @Column({ unique: true })
  // email: string;

  @Column({ name: 'phone_no' })
  phoneNo: string;

  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @Column({
    type: 'enum',
    enum: OrganizationStatus,
    default: OrganizationStatus.ACTIVE,
  })
  status: OrganizationStatus;

  @OneToMany(() => User, (user) => user.organization)
  admins: User[];

  @OneToMany(() => OrgMember, (orgMember) => orgMember.organization)
  members: OrgMember[];

  @OneToMany(() => Group, (group) => group.organization)
  groups: Group[];
}
