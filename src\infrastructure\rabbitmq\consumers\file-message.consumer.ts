// queue/consumers/file-message.consumer.ts
import { Injectable } from '@nestjs/common';
import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { SocketGateway } from '../../../infrastructure/socket/socket.gateway';
import { DataSource } from 'typeorm';
import { GroupMessage } from '../../../modules/messages/entities/group-message.entity';
import { tailorMessage } from '../../../common/utils/tailor-message.util';
import { StorageService } from '../../../core/storage/storage.service';

@Injectable()
export class FileMessageConsumer {
  constructor(
    private readonly dataSource: DataSource,
    private readonly socketGateway: SocketGateway,
    private readonly storageService: StorageService,
  ) {}

  @RabbitSubscribe({
    exchange: 'message_exchange',
    routingKey: 'message.file.send',
    queue: 'message_file_queue',
  })
  async handleFileMessage(payload: { messageId: string }) {
    const message = await this.dataSource.getRepository(GroupMessage).findOne({
      where: { id: Number(payload.messageId) },
      relations: ['sender', 'file', 'replyTo', 'replyTo.sender'],
    });

    if (!message) return;

    let signedUrl = '';
    if (message.file?.fileUrl) {
      signedUrl = await this.storageService.generateSignedUrl(
        message.file.fileUrl,
        24 * 60 * 60, // 1 day in seconds
      );
    }

    let signedAvatarUrl = '';
    if (message.sender?.imageUrl) {
      signedAvatarUrl = await this.storageService.generateSignedUrl(
        message.sender?.imageUrl,
        24 * 60 * 60, // 1 day in seconds
      );
    }

    const tailored = tailorMessage(message);

    // Override fileUrl with signed URL if available
    if (signedUrl) {
      tailored.fileUrl = signedUrl;
    }

    if (signedAvatarUrl) {
      tailored.sender.imageUrl = signedAvatarUrl;
    }

    this.socketGateway.server
      .to(String(message.groupId))
      .emit('new_message', tailored);
  }
}
