import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { BaseEvent } from '../../common/events';
import { EventMetricsService } from '../monitoring/services/event-metrics.service';
import { EventStoreService } from '../../modules/event-store/event-store.service';
@Injectable()
export class EventBusService {
  private readonly logger = new Logger(EventBusService.name);

  constructor(
    private readonly emitter: EventEmitter2,
    private readonly metricsService?: EventMetricsService,
    private readonly eventStore?: EventStoreService,
  ) {}

  /**
   * Publish a domain event (persistent + monitored by default)
   */
  async publish<T extends BaseEvent>(
    event: T,
    options: { persist?: boolean; monitor?: boolean } = {},
  ): Promise<void> {
    const { persist = true, monitor = true } = options;

    if (monitor && this.metricsService) {
      this.metricsService.recordEventEmitted(event);
    }

    if (persist && this.eventStore) {
      await this.eventStore.store(event);
    }

    this.logger.debug(`Publishing event: ${event.name}`);
    this.emitter.emit(event.name, event);
  }

  /**
   * Publish asynchronously
   */
  async publishAsync<T extends BaseEvent>(
    event: T,
    options?: { persist?: boolean; monitor?: boolean },
  ): Promise<void> {
    setImmediate(() => this.publish(event, options));
  }

  /**
   * Emit a lightweight in-memory signal (no persistence, no monitoring)
   */
  emit(eventName: string, payload?: any): void {
    this.logger.debug(`Emitting in-memory event: ${eventName}`);
    this.emitter.emit(eventName, payload);
  }

  /**
   * Emit asynchronously (lightweight)
   */
  emitAsync(eventName: string, payload?: any): void {
    setImmediate(() => this.emit(eventName, payload));
  }

  /**
   * Listen to events (both domain & lightweight)
   */
  on(eventName: string, listener: (...args: any[]) => void): void {
    this.emitter.on(eventName, listener);
  }

  once(eventName: string, listener: (...args: any[]) => void): void {
    this.emitter.once(eventName, listener);
  }

  off(eventName: string, listener: (...args: any[]) => void): void {
    this.emitter.off(eventName, listener);
  }

  /**
   * Introspection helpers
   */
  listenerCount(eventName: string): number {
    return this.emitter.listenerCount(eventName);
  }

  listeners(eventName: string): Function[] {
    return this.emitter.listeners(eventName);
  }
}
