import { OnEvent } from '@nestjs/event-emitter';
import { EVENT_NAMES } from 'src/common/constants/event-names';
import {
  MessageDeliveryAttemptEvent,
  MessageDeliveryFailedEvent,
  MessageDeliverySuccessEvent,
} from 'src/common/events/message.events';
import { GroupMessage } from '../entities/group-message.entity';
import { Repository } from 'typeorm';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
@Injectable()
export class MessageDeliveryHandler {
  private readonly logger = new Logger(MessageDeliveryHandler.name);

  constructor(
    @InjectRepository(GroupMessage)
    private readonly groupMessageRepo: Repository<GroupMessage>,
  ) {}

  @OnEvent(EVENT_NAMES.MESSAGE_DELIVERY_ATTEMPT, { async: true })
  async handleDeliveryAttempt(event: MessageDeliveryAttemptEvent) {}

  @OnEvent(EVENT_NAMES.MESSAGE_DELIVERY_FAILED, { async: true })
  async handleDeliveryFailed(event: MessageDeliveryFailedEvent) {
    this.logger.warn(
      `Message ${event.messageId} delivery failed: ${event.reason}`,
    );

    await this.groupMessageRepo.update(event.messageId, {
      deliveredCount: -1, // Mark as permanently failed
    });
  }

  @OnEvent(EVENT_NAMES.MESSAGE_DELIVERY_SUCCESS, { async: true })
  async handleDeliverySuccess(event: MessageDeliverySuccessEvent) {
    this.logger.log(
      `Message ${event.messageId} delivered to ${event.deliveredToCount} members`,
    );

    await this.groupMessageRepo.update(event.messageId, {
      deliveredCount: () => 'delivered_count + 1',
    });
  }
}
