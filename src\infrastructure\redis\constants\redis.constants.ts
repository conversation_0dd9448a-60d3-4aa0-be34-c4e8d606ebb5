export const REDIS_CLIENT = 'REDIS_CLIENT';
export const REDIS_PUBLISHER = 'REDIS_PUBLISHER';
export const REDIS_SUBSCRIBER = 'REDIS_SUBSCRIBER';

// Key patterns
export const REDIS_KEY_PREFIX = 'chat_app';
export const FCM_TOKEN_KEY = `${REDIS_KEY_PREFIX}:fcm_token`;
export const ONLINE_PRESENCE_KEY = `${REDIS_KEY_PREFIX}:presence`;
export const USER_SESSIONS_KEY = `${REDIS_KEY_PREFIX}:sessions`;
export const ONLINE_MEMBERS_HASH = `${REDIS_KEY_PREFIX}:online_members`;

export const REFRESH_TOKEN_KEY = 'auth:refresh_token';

export const REFRESH_CHALLENGE = 'refresh_challenge';
export const LOGIN_CHALLENGE = 'login_challenge';

// TTL for refresh tokens (in seconds)
export const REFRESH_TOKEN_TTL = 7 * 24 * 60 * 60;
