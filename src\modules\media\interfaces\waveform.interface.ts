/**
 * Waveform Interfaces
 * 
 * Shared TypeScript interfaces for waveform-related operations
 * across the media module and other parts of the application.
 */

export interface WaveformOptions {
  samples?: number;
  channels?: number;
  normalize?: boolean;
  precision?: number;
}

export interface WaveformData {
  peaks: number[];
  duration: number;
  sampleRate: number;
  channels: number;
  samples: number;
  startTime?: number;
  endTime?: number;
}

export interface WaveformImageOptions {
  width?: number;
  height?: number;
  backgroundColor?: string;
  waveColor?: string;
  progressColor?: string;
}

export interface WaveformSVGOptions {
  width?: number;
  height?: number;
  strokeWidth?: number;
  strokeColor?: string;
  fillColor?: string;
}

export interface WaveformMetadata {
  duration: number;
  sampleRate: number;
  channels: number;
  bitDepth: number;
  format: string;
  fileSize: number;
}

export interface SilenceRegion {
  startTime: number;
  endTime: number;
  duration: number;
}

export interface WaveformMarker {
  time: number;
  label: string;
  color?: string;
  type?: 'cue' | 'region' | 'bookmark';
}

export interface WaveformGenerationRequest {
  fileId: string;
  audioPath: string;
  options?: WaveformOptions;
  outputFormat?: 'json' | 'image' | 'svg';
  cacheResult?: boolean;
}

export interface WaveformGenerationResponse {
  success: boolean;
  data?: WaveformData;
  imagePath?: string;
  svgPath?: string;
  jsonPath?: string;
  cached?: boolean;
  error?: string;
}

export interface WaveformVisualizationConfig {
  theme: 'light' | 'dark' | 'custom';
  colors: {
    background: string;
    waveform: string;
    progress: string;
    cursor: string;
    markers: string;
  };
  dimensions: {
    width: number;
    height: number;
    barWidth?: number;
    barGap?: number;
  };
  features: {
    showProgress: boolean;
    showMarkers: boolean;
    showTimeAxis: boolean;
    showAmplitudeAxis: boolean;
    enableZoom: boolean;
    enableSelection: boolean;
  };
}

export interface AudioAnalysisResult {
  waveform: WaveformData;
  metadata: WaveformMetadata;
  silenceRegions: SilenceRegion[];
  peakLevels: {
    max: number;
    average: number;
    rms: number;
  };
  frequencyAnalysis?: {
    dominantFrequency: number;
    spectralCentroid: number;
    spectralRolloff: number;
  };
}
