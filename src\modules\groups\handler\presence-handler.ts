import { Inject, Injectable, Logger } from '@nestjs/common';
import { GroupMembersService } from '../services/group-member.service';
import { OnEvent } from '@nestjs/event-emitter';
import { EVENT_NAMES } from 'src/common/constants/event-names';
import { PresenceUpdatedEvent } from 'src/common/events/presence.events';
import { ISocketRoomService } from 'src/infrastructure/socket/interfaces/socket-room.interface';

@Injectable()
export class PresenceNotifyHandler {
  private readonly logger = new Logger(PresenceNotifyHandler.name);
  constructor(
    private readonly groupMembersService: GroupMembersService,
    @Inject('ISocketRoomService')
    private readonly socketRoomService: ISocketRoomService,
  ) {}

  @OnEvent(EVENT_NAMES.PRESENCE_UPDATED)
  async onPresenceUpdated(event: PresenceUpdatedEvent) {
    try {
      const { payload } = event;

      // Get associated members (members who share groups)
      const associatedMemberIds =
        await this.groupMembersService.getAssociatedMemberIds(payload.memberId);

      associatedMemberIds.forEach((memberId) => {
        this.socketRoomService.broadcastToMember(
          memberId,
          EVENT_NAMES.PRESENCE_NOTIFY,
          {
            memberId: payload.memberId,
            status: payload.status,
            timestamp: payload.timestamp,
            lastSeen: payload.lastSeen,
          },
        );
      });

      this.logger.debug(
        `Notified ${associatedMemberIds.length} associated members about presence change for member ${payload.memberId}`,
      );
    } catch (error) {
      this.logger.error('Error handling presence updated event:', error);
    }
  }
}
