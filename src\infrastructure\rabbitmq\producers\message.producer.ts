import { AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import { Injectable } from '@nestjs/common';
import {
  MESSAGE_DELIVERY_ROUTING_KEY,
  MESSAGE_EXCHANGE,
} from 'src/common/constants';
import {
  MemberNotificationInfo,
  PushNotification,
} from 'src/infrastructure/notification/services/notification.service';

export interface MessageDeliveryTaskPayload {
  messageId: number;
  memberNotificationInfo: MemberNotificationInfo[];
  notificationPayload: PushNotification;
  retryCount?: number;
}
@Injectable()
export class MessageProducer {
  constructor(private readonly amqpConnection: AmqpConnection) {}

  async sendMessageDeliveryTask(payload: MessageDeliveryTaskPayload) {
    const message = {
      ...payload,
      createdAt: new Date(),
      retryCount: payload.retryCount || 0,
    };

    await this.amqpConnection.publish(
      MESSAGE_EXCHANGE,
      MESSAGE_DELIVERY_ROUTING_KEY,
      message,
    );
  }
}
