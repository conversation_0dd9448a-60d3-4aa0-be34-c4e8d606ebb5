import { IsNotEmpty, IsString, <PERSON>N<PERSON><PERSON> } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class MobileLogoutDto {
  @ApiProperty({
    description: 'Refresh token of the user for mobile logout',
    example: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
  })
  @IsNotEmpty()
  @IsString()
  refreshToken: string;

  @ApiProperty({
    description: 'Member ID of the user',
    example: 123,
  })
  @IsNotEmpty()
  @IsNumber()
  memberId: number;

  @ApiProperty({
    description: 'Device ID of the mobile device',
    example: 'device-uuid-1234',
  })
  @IsNotEmpty()
  @IsString()
  deviceId: string;
}
