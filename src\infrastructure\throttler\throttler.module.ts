import { Module } from '@nestjs/common';
import { ThrottlerModule } from '@nestjs/throttler';
import { RedisModule } from '../redis/redis.module';
import { RedisService } from '../redis/services/redis.service';
import { ThrottlerRedisStorage } from './throttler-redis.storage';

@Module({
  imports: [
    ThrottlerModule.forRootAsync({
      imports: [RedisModule],
      inject: [RedisService],
      useFactory: (redisService: RedisService) => ({
        storage: new ThrottlerRedisStorage(redisService),
        throttlers: [
          {
            name: 'global',
            ttl: 60,
            limit: 100,
          },
        ],
      }),
    }),
  ],
  exports: [ThrottlerModule],
})
export class AppThrottlerModule {}
