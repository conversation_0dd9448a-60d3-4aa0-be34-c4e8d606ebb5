import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { GroupMessage } from './entities/group-message.entity';
import { GroupMessageRead } from './entities/message-read-receipt.entity';
import { GroupMessagesService } from './services/group-messages.service';
import { MessagesController } from './controllers/messages.controller';
import { MembersModule } from '../members/members.module';
import { GroupsModule } from '../groups/groups.module';
import { PrivateMessage } from './entities/private-message.entity';
import { MessageThread } from './entities/message-thread.entity';
import { SyncState } from './entities/sync-state.entity';
import { InfrastructureModule } from 'src/infrastructure/infrastructure.module';
import { MessageDeliveryHandler } from './handlers/message-delivered.handler';
import { SendMessageRequestedHandler } from './handlers/send-message-requested.handler';
import { MessageSentHandler } from './handlers/message-sent.handler';
import { SyncStateService } from './services/sync-state.service';
import { MessageThreadService } from './services/message-threads.service';
import { AuditModule } from '../audit/audit.module';
import { StorageModule } from 'src/core/storage/storage.module';
import { PrivateMessageService } from './services/pvt-messages.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      GroupMessage,
      GroupMessageRead,
      PrivateMessage,
      MessageThread,
      SyncState,
    ]),
    MembersModule,
    GroupsModule,
    InfrastructureModule,
    AuditModule,
    StorageModule,
  ],
  providers: [
    GroupMessagesService,
    MessageDeliveryHandler,
    SendMessageRequestedHandler,
    MessageSentHandler,
    SyncStateService,
    MessageThreadService,
    PrivateMessageService,
  ],
  controllers: [MessagesController],
  exports: [
    GroupMessagesService,
    MessageThreadService,
    SyncStateService,
    PrivateMessageService,
  ],
})
export class MessagesModule {}
