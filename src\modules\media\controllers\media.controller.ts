import {
  Controller,
  Post,
  Get,
  Delete,
  Param,
  Query,
  UploadedFile,
  UseInterceptors,
  ParseIntPipe,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { MediaService } from '../services/media.service';
import { MediaUploadService } from '../services/media-upload.service';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';

@ApiTags('Media')
@ApiBearerAuth()
@Controller('media')
export class MediaController {
  constructor(
    private readonly mediaService: MediaService,
    private readonly mediaUploadService: MediaUploadService,
  ) {}

  @Post('upload')
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload a media file' })
  @ApiBody({
    description: 'Media file to upload',
    type: 'multipart/form-data',
  })
  @ApiQuery({ name: 'userId', required: true, type: Number })
  @ApiResponse({ status: 201, description: 'File uploaded successfully' })
  async uploadMedia(
    @UploadedFile() file: Express.Multer.File,
    @Query('userId', ParseIntPipe) userId: number,
  ) {
    return await this.mediaUploadService.uploadFile(file, userId);
  }

  @Get(':fileId/metadata')
  @ApiOperation({ summary: 'Get media file metadata' })
  @ApiParam({ name: 'fileId', description: 'Media file ID' })
  @ApiResponse({ status: 200, description: 'Metadata fetched successfully' })
  async getMediaMetadata(@Param('fileId') fileId: string) {
    return await this.mediaService.getMediaMetadata(fileId);
  }

  @Get('user/:userId')
  @ApiOperation({ summary: 'Get media files for a specific user' })
  @ApiParam({ name: 'userId', description: 'User ID' })
  @ApiQuery({ name: 'limit', required: false, type: Number })
  @ApiQuery({ name: 'offset', required: false, type: Number })
  @ApiResponse({ status: 200, description: 'User media files fetched' })
  async getUserMediaFiles(
    @Param('userId', ParseIntPipe) userId: number,
    @Query('limit', ParseIntPipe) limit?: number,
    @Query('offset', ParseIntPipe) offset?: number,
  ) {
    return await this.mediaService.getUserMediaFiles(userId, limit, offset);
  }

  @Get(':fileId')
  @ApiOperation({ summary: 'Get media file details' })
  @ApiParam({ name: 'fileId', description: 'Media file ID' })
  @ApiResponse({ status: 200, description: 'Media file details returned' })
  async getMediaFile(@Param('fileId') fileId: string) {
    return await this.mediaService.getMediaFileById(fileId);
  }

  @Delete(':fileId')
  @ApiOperation({ summary: 'Delete a media file' })
  @ApiParam({ name: 'fileId', description: 'Media file ID' })
  @ApiQuery({ name: 'userId', type: Number })
  @ApiResponse({ status: 200, description: 'Media file deleted successfully' })
  async deleteMediaFile(
    @Param('fileId') fileId: string,
    @Query('userId', ParseIntPipe) userId: number,
  ) {
    await this.mediaService.deleteMediaFile(fileId, userId);
    return { message: 'Media file deleted successfully' };
  }

  @Post(':fileId/process')
  @ApiOperation({
    summary: 'Process a media file (e.g., compression, thumbnail)',
  })
  @ApiParam({ name: 'fileId', description: 'Media file ID' })
  @ApiQuery({
    name: 'operations',
    description: 'Comma-separated operations',
    type: String,
  })
  @ApiResponse({ status: 200, description: 'Media processing started' })
  async processMediaFile(
    @Param('fileId') fileId: string,
    @Query('operations') operations: string,
  ) {
    return { message: 'Media processing started', fileId, operations };
  }
}
