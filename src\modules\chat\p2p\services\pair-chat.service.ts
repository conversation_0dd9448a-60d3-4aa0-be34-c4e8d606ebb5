import { Injectable, NotFoundException } from '@nestjs/common';
import { QueryRunner } from 'typeorm';
import { PairChat } from '../entities/pair-chats.entity';

@Injectable()
export class PairChatService {
  async getChatMembers(
    chatId: number,
    queryRunner: QueryRunner,
  ): Promise<{ member1Id: number; member2Id: number }> {
    const pairChat = await queryRunner.manager.findOne(PairChat, {
      where: { id: chatId },
      select: ['member1Id', 'member2Id'],
    });

    if (!pairChat) {
      throw new NotFoundException(`PairChat ${chatId} not found`);
    }

    return { member1Id: pairChat.member1Id, member2Id: pairChat.member2Id };
  }

  async findOrCreatePairChat(
    member1Id: number,
    member2Id: number,
    queryRunner: QueryRunner,
    keyVersion?: number,
  ): Promise<number> {
    // Ensure consistent ordering
    const [smallerId, largerId] =
      member1Id < member2Id ? [member1Id, member2Id] : [member2Id, member1Id];

    let pairChat = await queryRunner.manager.findOne(PairChat, {
      where: { member1Id: smallerId, member2Id: largerId, isActive: true },
    });

    if (!pairChat) {
      pairChat = queryRunner.manager.create(PairChat, {
        member1Id: smallerId,
        member2Id: largerId,
        currentKeyVersion: keyVersion || 1,
        isActive: true,
        lastMessageAt: new Date(),
      });

      pairChat = await queryRunner.manager.save(PairChat, pairChat);
    }

    return pairChat.id;
  }
}
