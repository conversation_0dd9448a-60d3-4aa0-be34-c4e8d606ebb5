import { ApiProperty } from '@nestjs/swagger';
import {
  IsArray,
  IsDate,
  IsInt,
  IsPositive,
  ValidateNested,
} from 'class-validator';
import { Type } from 'class-transformer';

class ReadMessageDto {
  @ApiProperty({ example: 1001, description: 'ID of the message read' })
  @IsInt()
  @IsPositive()
  messageId: number;

  @ApiProperty({
    example: '2025-08-26T12:00:00.000Z',
    description: 'Timestamp when message was read',
  })
  @IsDate()
  @Type(() => Date)
  readAt: Date;
}

export class MessageReadDto {
  @ApiProperty({ example: 50, description: 'Group ID where message belongs' })
  @IsInt()
  @IsPositive()
  groupId: number;

  @ApiProperty({ example: 3, description: 'User ID of the reader' })
  @IsInt()
  @IsPositive()
  readerId: number;

  @ApiProperty({
    type: [ReadMessageDto],
    description: 'List of messages with read timestamps',
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => ReadMessageDto)
  messages: ReadMessageDto[];
}
