import {
  JwtAdminPayload,
  JwtOrgMemberPayload,
  JwtPayload,
} from './jwt-payload.interface';

// Base type for refresh token
export interface RefreshTokenPayloadBase {
  tokenId: string;
}

// Generic mapped type: any JwtPayload + tokenId
export type RefreshTokenPayload<T extends JwtPayload = JwtPayload> = T &
  RefreshTokenPayloadBase;

// Usage examples
export type AdminRefreshToken = RefreshTokenPayload<JwtAdminPayload>;
export type MemberRefreshToken = RefreshTokenPayload<JwtOrgMemberPayload>;
