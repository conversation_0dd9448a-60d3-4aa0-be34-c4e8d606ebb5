import { User } from '../../users/entities/user.entity';
import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  OneToMany,
  CreateDateColumn,
  UpdateDateColumn,
} from 'typeorm';
import { GroupChangeLog } from '../../audit/entities/group-change-logs.entity';

@Entity('groups')
export class Group {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'org_id' })
  orgId: number;

  @Column()
  name: string;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'image_url', nullable: true })
  imageUrl: string;

  @Column({ name: 'created_by' })
  createdBy: number;

  @CreateDateColumn({ name: 'created_at', type: 'timestamptz' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamptz' })
  updatedAt: Date;

  @Column({ type: 'timestamp', nullable: true })
  deletedAt: Date | null;

  @Column({ type: 'int', nullable: true })
  deletedBy: number | null;

  @Column({ name: 'current_key_version', default: 1 })
  currentKeyVersion: number;

  @ManyToOne('Organization', 'groups')
  @JoinColumn({ name: 'org_id' })
  organization: any;

  @ManyToOne(() => User, (user) => user.createdGroups)
  @JoinColumn({ name: 'created_by' })
  creator: User;

  @ManyToOne(() => User, (user) => user.deletedGroups, { nullable: true })
  @JoinColumn({ name: 'deleted_by' })
  deleter: User;

  @OneToMany('GroupMember', 'group')
  members: any[];

  @OneToMany('GroupMessage', 'group')
  messages: any[];

  @OneToMany('GroupEncryptionKey', 'group')
  encryptionKeys: any[];

  @OneToMany(() => GroupChangeLog, (changeLog) => changeLog.group)
  changeLogs: GroupChangeLog[];
}
