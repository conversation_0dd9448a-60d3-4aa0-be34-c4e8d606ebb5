import { Injectable, Logger } from '@nestjs/common';
import { EventMetricsService } from './event-metrics.service';
import { EventStoreService } from '../../../modules/event-store/event-store.service';

export interface HealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  uptime: number;
  checks: {
    events: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      metrics?: any;
      issues?: string[];
    };
    eventStore: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      lastEvent?: string;
      issues?: string[];
    };
    memory: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      usage: {
        used: number;
        total: number;
        percentage: number;
      };
      issues?: string[];
    };
  };
}

@Injectable()
export class HealthCheckService {
  private readonly logger = new Logger(HealthCheckService.name);

  constructor(
    private readonly metricsService: EventMetricsService,
    private readonly eventStore: EventStoreService,
  ) {}

  async getHealthStatus(): Promise<HealthStatus> {
    const timestamp = new Date().toISOString();
    const uptime = process.uptime();

    try {
      const [eventHealth, eventStoreHealth, memoryHealth] = await Promise.all([
        this.checkEventHealth(),
        this.checkEventStoreHealth(),
        this.checkMemoryHealth(),
      ]);

      const overallStatus = this.determineOverallStatus([
        eventHealth.status,
        eventStoreHealth.status,
        memoryHealth.status,
      ]);

      return {
        status: overallStatus,
        timestamp,
        uptime,
        checks: {
          events: eventHealth,
          eventStore: eventStoreHealth,
          memory: memoryHealth,
        },
      };
    } catch (error) {
      this.logger.error('Error performing health check:', error);
      return {
        status: 'unhealthy',
        timestamp,
        uptime,
        checks: {
          events: { status: 'unhealthy', issues: ['Health check failed'] },
          eventStore: { status: 'unhealthy', issues: ['Health check failed'] },
          memory: {
            status: 'unhealthy',
            usage: { used: 0, total: 0, percentage: 0 },
            issues: ['Health check failed'],
          },
        },
      };
    }
  }

  private async checkEventHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    metrics?: any;
    issues?: string[];
  }> {
    try {
      const healthStatus = this.metricsService.getHealthStatus();
      const metrics = this.metricsService.getMetrics();

      return {
        status: healthStatus.healthy ? 'healthy' : 'degraded',
        metrics,
        issues: healthStatus.issues,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        issues: [`Event health check failed: ${error.message}`],
      };
    }
  }

  private async checkEventStoreHealth(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    lastEvent?: string;
    issues?: string[];
  }> {
    try {
      const recentEvents = await this.eventStore.getEvents(
        undefined,
        undefined,
        undefined,
        1,
      );

      return {
        status: 'healthy',
        lastEvent:
          recentEvents.length > 0
            ? recentEvents[0].timestamp.toISOString()
            : 'No events found',
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        issues: [`Event store health check failed: ${error.message}`],
      };
    }
  }

  private checkMemoryHealth(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    usage: { used: number; total: number; percentage: number };
    issues?: string[];
  } {
    const memoryUsage = process.memoryUsage();
    const totalMemory = memoryUsage.heapTotal;
    const usedMemory = memoryUsage.heapUsed;
    const percentage = (usedMemory / totalMemory) * 100;

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
    const issues: string[] = [];

    if (percentage > 90) {
      status = 'unhealthy';
      issues.push(`Critical memory usage: ${percentage.toFixed(2)}%`);
    } else if (percentage > 75) {
      status = 'degraded';
      issues.push(`High memory usage: ${percentage.toFixed(2)}%`);
    }

    return {
      status,
      usage: {
        used: usedMemory,
        total: totalMemory,
        percentage: Math.round(percentage * 100) / 100,
      },
      issues: issues.length > 0 ? issues : undefined,
    };
  }

  private determineOverallStatus(
    statuses: ('healthy' | 'degraded' | 'unhealthy')[],
  ): 'healthy' | 'degraded' | 'unhealthy' {
    if (statuses.includes('unhealthy')) {
      return 'unhealthy';
    }
    if (statuses.includes('degraded')) {
      return 'degraded';
    }
    return 'healthy';
  }

  async isHealthy(): Promise<boolean> {
    const health = await this.getHealthStatus();
    return health.status === 'healthy';
  }
}
