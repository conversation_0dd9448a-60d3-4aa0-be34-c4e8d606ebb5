// Working implementation for current NestJS Throttler
import { Injectable, ExecutionContext, Logger, Inject } from '@nestjs/common';
import {
  ThrottlerGuard,
  ThrottlerStorage,
  ThrottlerModuleOptions,
} from '@nestjs/throttler';
import { Reflector } from '@nestjs/core';
import { THROTTLER_OPTIONS } from '@nestjs/throttler/dist/throttler.constants';
import * as argon2 from 'argon2';
import {
  THROTTLER_CUSTOM_NAME,
  THROTTLER_RATE_LIMIT_OPTIONS,
} from '../constants/throttler.constants';

export interface RateLimitOptions {
  limit: number;
  ttl: number;
  name?: string;
}

@Injectable()
export class AdvancedThrottlerGuard extends ThrottlerGuard {
  private readonly logger = new Logger(AdvancedThrottlerGuard.name);

  constructor(
    @Inject(THROTTLER_OPTIONS) options: ThrottlerModuleOptions,
    storage: ThrottlerStorage,
    reflector: Reflector,
  ) {
    super(options, storage, reflector);
  }

  protected async getTracker(req: Record<string, any>): Promise<string> {
    const rawIp = req.headers?.['x-forwarded-for'] || req.socket?.remoteAddress;
    const ip = Array.isArray(rawIp)
      ? rawIp[0]
      : rawIp?.toString().split(',')[0]?.trim();

    const userId = req.user?.id ? String(req.user.id) : 'anonymous';
    const userAgent = req.headers?.['user-agent'] || 'no-agent';

    const userAgentHash = await this.hashWithArgon2(userAgent.substring(0, 50));
    return `${ip}-${userId}-${userAgentHash}`;
  }

  private async hashWithArgon2(str: string): Promise<string> {
    try {
      const hash = await argon2.hash(str, {
        type: argon2.argon2id,
        memoryCost: 2 ** 16,
        timeCost: 3,
        parallelism: 1,
        hashLength: 16,
      });

      return hash.split('$').pop() || hash;
    } catch (error) {
      this.logger.error('Failed to hash with argon2:', error);
      return this.simpleHash(str).toString();
    }
  }

  private simpleHash(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      hash = (hash << 5) - hash + str.charCodeAt(i);
      hash |= 0;
    }
    return hash;
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    if (context.getType() !== 'http') {
      // Allow non-HTTP contexts
      return true;
    }
    const req = context.switchToHttp().getRequest();
    const res = context.switchToHttp().getResponse();

    // Skip rate limiting for specific endpoints
    if (['/health', '/metrics', '/favicon.ico'].includes(req.url)) {
      return true;
    }

    // Get custom rate limit options from decorator
    const handler = context.getHandler();
    const rateLimitOptions = this.reflector.get<RateLimitOptions>(
      THROTTLER_RATE_LIMIT_OPTIONS,
      handler,
    );

    if (rateLimitOptions) {
      return this.handleCustomRateLimit(context, rateLimitOptions);
    }

    // Use default throttling
    try {
      return await super.canActivate(context);
    } catch (error) {
      this.logger.error('Error in rate limiting:', error);
      return true;
    }
  }

  private async handleCustomRateLimit(
    context: ExecutionContext,
    options: RateLimitOptions,
  ): Promise<boolean> {
    const req = context.switchToHttp().getRequest();
    const res = context.switchToHttp().getResponse();

    const key = await this.getTracker(req);

    // This storage is automatically your Redis storage from AppThrottlerModule
    const storage = this.getStorageService(); // This gets your ThrottlerRedisStorage instance

    const throttlerName = options.name || THROTTLER_CUSTOM_NAME;

    try {
      // This calls your Redis storage's increment method
      const record = await storage.increment(
        key,
        options.ttl,
        options.limit,
        options.ttl,
        throttlerName,
      );

      // Set rate limit headers
      if (res && typeof res.setHeader === 'function') {
        res.setHeader('X-RateLimit-Limit', options.limit);
        res.setHeader(
          'X-RateLimit-Remaining',
          Math.max(0, options.limit - record.totalHits),
        );
        res.setHeader(
          'X-RateLimit-Reset',
          Math.ceil(Date.now() / 1000) + options.ttl,
        );
      }

      if (record.totalHits > options.limit || record.isBlocked) {
        this.logger.warn(`Rate limit exceeded for key: ${key}`);

        if (
          res &&
          typeof res.status === 'function' &&
          typeof res.json === 'function'
        ) {
          res.status(429).json({
            statusCode: 429,
            message: `Too Many Requests. Try again in ${options.ttl} seconds.`,
            retryAfter: options.ttl,
          });
        }
        return false;
      }

      return true;
    } catch (error) {
      this.logger.error('Error in custom rate limiting:', error);
      return true;
    }
  }

  // This method accesses the Redis storage that was injected by ThrottlerModule
  private getStorageService(): ThrottlerStorage {
    // The parent ThrottlerGuard has the storage injected automatically
    // This is your ThrottlerRedisStorage instance
    return (this as any).storageService;
  }

  protected async getTrackersFromRequest(
    context: ExecutionContext,
  ): Promise<string[]> {
    const req = context.switchToHttp().getRequest();
    const customTracker = await this.getTracker(req);
    return [customTracker];
  }

  protected generateKey(
    context: ExecutionContext,
    suffix: string,
    name: string,
  ): string {
    const req = context.switchToHttp().getRequest();
    // Create a unique key using our custom tracker
    return `throttle:${name}:${suffix}`;
  }
}
