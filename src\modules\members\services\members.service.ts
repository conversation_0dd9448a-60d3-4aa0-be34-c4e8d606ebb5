import {
  Injectable,
  ConflictException,
  NotFoundException,
  BadRequestException,
  UnauthorizedException,
} from '@nestjs/common';
import { InjectDataSource, InjectRepository } from '@nestjs/typeorm';
import { In, Repository, IsNull, QueryRunner } from 'typeorm';
import { MemberFcmToken } from '../entities/member-fcm-token.entity';
import { OrgMember } from '../entities/org-member.entity';
import { CreateMemberDto } from '../dto/create-member.dto';
import { UpdateMemberDto } from '../dto/update-member.dto';
import { CreateFcmTokenDto } from '../dto/create-fcm.dto';
import { EncryptionService } from '../../../core/encryption/encryption.service';
import { UsersService } from '../../users/services/users.service';
import { OrganizationsService } from '../../organization/services/organizations.service';
import { IdentityKeyService } from '../../../modules/security/services/identity-key.service';
import { StorageService } from '../../../core/storage/storage.service';
import { DataSource } from 'typeorm';
import { Logger } from '@nestjs/common';
import { GroupLogService } from '../../../modules/audit/services/group.log.service';
import { MemberResponseDto } from '../dto/member-response.dto';
import {
  EncryptedPrivateKey,
  IdentityKeyEntity,
} from '../../security/entities/identity-key.entity';
import { AuthenticatedUser } from 'src/common/types/authenticate-user.types';
import { FcmService } from 'src/infrastructure/notification/services/fcm.service';
import { CacheService } from 'src/infrastructure/redis/services/cache.service';

@Injectable()
export class MembersService {
  private readonly logger = new Logger(MembersService.name);
  constructor(
    @InjectRepository(OrgMember)
    private readonly memberRepository: Repository<OrgMember>,
    @InjectRepository(MemberFcmToken)
    private readonly fcmTokenRepository: Repository<MemberFcmToken>,
    private readonly encryptionService: EncryptionService,
    private readonly userService: UsersService,
    private readonly organizationService: OrganizationsService,
    private readonly identityKeyService: IdentityKeyService,
    private readonly storageService: StorageService,
    private readonly fcmService: FcmService,
    private readonly cacheService: CacheService,
    private readonly groupLogService: GroupLogService,
    @InjectDataSource()
    private readonly dataSource: DataSource,
  ) {}

  async create(
    createMemberDto: CreateMemberDto,
    userId: number,
  ): Promise<OrgMember> {
    // 1. Check if member exists by email or phone
    const existingMember = await this.memberRepository.findOne({
      where: [
        { email: createMemberDto.email },
        { phoneNo: createMemberDto.phoneNo },
      ],
    });

    if (existingMember) {
      throw new BadRequestException(
        'A member with this email or phone number already exists',
      );
    }

    // 2. Check if user already exists with this email
    const existingUser = await this.userService.findByMail(
      createMemberDto.email,
    );
    if (existingUser) {
      throw new ConflictException('User with this email already exists');
    }

    // 3. Get current user (creator)
    const user = await this.userService.findById(userId);
    if (user.roleId === 1) {
      throw new BadRequestException(
        'Product admin is not allowed to create organization members',
      );
    }

    // 4. Validate organization
    const organization = await this.organizationService.findOne(user.orgId);
    if (!organization) {
      throw new NotFoundException(
        `Organization with ID ${user.orgId} not found`,
      );
    }

    // 5. Create member entity (unsaved)
    const member = this.memberRepository.create({
      name: createMemberDto.name,
      phoneNo: createMemberDto.phoneNo,
      email: createMemberDto.email,
      imageUrl: createMemberDto.fileUrl,
      orgId: user.orgId,
      createdBy: userId,
    });

    // 6. Save member first to get an ID
    const savedMember = await this.memberRepository.save(member);

    // Generate identity key pair
    const { publicKey, secretKey } = this.encryptionService.generateKeyPair();

    const encryptedPrivateKey = this.encryptionService.encryptSecretKey(
      secretKey,
      createMemberDto.phoneNo,
    );

    //  Create identity key for saved member
    await this.identityKeyService.createIdentityKey(
      savedMember.id,
      publicKey,
      encryptedPrivateKey,
    );

    return savedMember;
  }

  async update(
    id: number,
    updateDto: UpdateMemberDto,
    user: AuthenticatedUser,
  ): Promise<MemberResponseDto> {
    return await this.dataSource.transaction(async (manager) => {
      const member = await manager.getRepository(OrgMember).findOne({
        where: { id },
        lock: { mode: 'pessimistic_write' },
      });

      if (!member)
        throw new NotFoundException(`Member with ID ${id} not found`);

      const identityKey = await manager
        .getRepository(IdentityKeyEntity)
        .findOne({ where: { memberId: member.id } });

      if (identityKey) {
        member.identityKey = identityKey;
      }

      if (user.orgId !== member.orgId) {
        throw new UnauthorizedException();
      }

      let newEncryptedPrivateKey: EncryptedPrivateKey =
        member.identityKey?.encryptedPrivateKey;

      // 🔑 Handle phone change and re-encryption
      const isPhoneChanged =
        updateDto.phoneNo && updateDto.phoneNo !== member.phoneNo;

      if (isPhoneChanged && member.identityKey?.encryptedPrivateKey) {
        try {
          const decrypted = this.encryptionService.decryptSecretKey(
            member.identityKey.encryptedPrivateKey,
            member.phoneNo,
          );

          const reEncrypted = this.encryptionService.encryptSecretKey(
            decrypted,
            updateDto.phoneNo,
          );

          newEncryptedPrivateKey = reEncrypted;

          await this.identityKeyService.updateIdentityKey(
            member.id,
            member.identityKey.publicKey,
            newEncryptedPrivateKey,
            manager,
          );
        } catch (err) {
          throw new BadRequestException(
            'Failed to re-encrypt private key after phone change',
          );
        }
      }

      await manager.update(OrgMember, id, {
        name: updateDto.name ?? member.name,
        imageUrl: updateDto.fileUrl ?? member.imageUrl,
        email: updateDto.email ?? member.email,
        phoneNo: updateDto.phoneNo ?? member.phoneNo,
      });

      const updatedMember = await manager.findOne(OrgMember, {
        where: { id },
        relations: ['identityKey'],
      });

      if (!updatedMember) {
        throw new NotFoundException(
          `Member with ID ${id} not found after update`,
        );
      }

      const loggableFields: Record<string, { old: any; new: any }> = {
        name: { old: member.name, new: updatedMember.name },
        email: { old: member.email, new: updatedMember.email },
        phoneNo: { old: member.phoneNo, new: updatedMember.phoneNo },
        imageUrl: { old: member.imageUrl, new: updatedMember.imageUrl },
      };

      // Only track encryptedPrivateKey if phone changed
      if (isPhoneChanged && member.identityKey?.encryptedPrivateKey) {
        loggableFields.encryptedPrivateKey = {
          old: member.identityKey.encryptedPrivateKey,
          new: newEncryptedPrivateKey,
        };
      }

      for (const [field, { old, new: newVal }] of Object.entries(
        loggableFields,
      )) {
        const oldValue =
          old !== undefined && old !== null ? String(old) : undefined;
        const newValue =
          newVal !== undefined && newVal !== null ? String(newVal) : undefined;

        if (oldValue !== newValue) {
          await this.groupLogService.logMemberUpdate(
            updatedMember.id,
            field,
            oldValue ?? '',
            newValue ?? '',
            manager,
          );
        }
      }

      return new MemberResponseDto(updatedMember);
    });
  }

  async findAll(orgId: number): Promise<OrgMember[]> {
    const members = await this.memberRepository.find({ where: { orgId } });
    return members;
  }

  async getMemberName(memberId: number): Promise<string> {
    const member = await this.memberRepository.findOne({
      where: { id: memberId },
      select: ['name'],
    });

    return member?.name || `Member ${memberId}`;
  }

  async findOne(id: number): Promise<OrgMember> {
    const member = await this.memberRepository.findOne({
      where: { id },
      relations: ['organization', 'groupMemberships', 'fcmTokens'],
    });

    if (!member) {
      throw new NotFoundException(`Member with ID ${id} not found`);
    }

    return member;
  }

  async findByPhoneNo(phoneNo: string): Promise<OrgMember> {
    const member = await this.memberRepository.findOne({ where: { phoneNo } });
    if (!member) {
      throw new NotFoundException(
        `Member with phone number ${phoneNo} not found`,
      );
    }
    return member;
  }

  async invalidToken(memberId: number, deviceId: string) {
    await this.fcmTokenRepository.update(
      { memberId, deviceId },
      { isActive: false, deletedAt: new Date() },
    );
  }

  async upsertFcmToken(
    queryRunner: QueryRunner,
    memberId: number,
    dto: CreateFcmTokenDto,
  ) {
    await queryRunner.manager.upsert(
      MemberFcmToken,
      {
        memberId,
        deviceId: dto.deviceId,
        fcmToken: dto.fcmToken,
        deviceType: dto.deviceType,
        isActive: true,
        lastUsedAt: new Date(),
      },
      {
        conflictPaths: ['deviceId', 'memberId'],
        skipUpdateIfNoValuesChanged: false,
      },
    );
  }

  async createFcmToken(
    memberId: number,
    dto: CreateFcmTokenDto,
  ): Promise<MemberFcmToken> {
    const member = await this.memberRepository.findOne({
      where: { id: memberId },
    });
    if (!member) {
      throw new NotFoundException(`Member with ID ${memberId} not found`);
    }

    await this.fcmTokenRepository.update(
      { fcmToken: dto.fcmToken },
      { isActive: false, deletedAt: new Date() },
    );

    const newToken = this.fcmTokenRepository.create({
      memberId,
      fcmToken: dto.fcmToken,
      deviceId: dto.deviceId,
      deviceType: dto.deviceType,
      isActive: true,
      lastUsedAt: new Date(),
    });

    return this.fcmTokenRepository.save(newToken);
  }

  async findActiveFcmTokens(memberIds: number[]): Promise<string[]> {
    if (!memberIds || memberIds.length === 0) return [];

    const tokens = await this.fcmTokenRepository.find({
      where: {
        memberId: In(memberIds),
        isActive: true,
        deletedAt: IsNull(),
      },
    });

    return tokens.map((token) => token.fcmToken);
  }

  /**
   * Check if member has any other active device besides the provided deviceId
   * @param memberId
   * @param currentDeviceId
   */
  async checkActiveSessionsOnOtherDevices(
    memberId: number,
    currentDeviceId: string,
  ): Promise<void> {
    const activeTokens = await this.fcmTokenRepository.find({
      where: { memberId, isActive: true },
    });

    // If no active tokens, allow login
    if (activeTokens.length === 0) {
      return;
    }

    // Validate FCM tokens
    const { valid, invalid } = await this.fcmService.validateTokens(
      activeTokens.map((t) => t.fcmToken),
    );

    // Clean up invalid tokens immediately
    if (invalid.length > 0) {
      const invalidTokens = activeTokens.filter((t) =>
        invalid.includes(t.fcmToken),
      );
      const invalidTokenIds = activeTokens
        .filter((token) => invalid.includes(token.fcmToken))
        .map((token) => token.id);

      await this.fcmTokenRepository.update(
        { id: In(invalidTokenIds) },
        { isActive: false },
      );
      await Promise.all(
        invalidTokens.map((t) =>
          this.cacheService.removeFcmToken(memberId, t.deviceId),
        ),
      );
    }

    // Only check for other devices among VALID tokens
    const validActiveTokens = activeTokens.filter((token) =>
      valid.includes(token.fcmToken),
    );

    const hasOtherValidActiveDevice = validActiveTokens.some(
      (token) => token.deviceId !== currentDeviceId,
    );

    if (hasOtherValidActiveDevice) {
      throw new BadRequestException(
        'You are already logged in on another device. Please logout first.',
      );
    }
  }

  async getVerifiedMembersWithSignedUrls(memberIds: number[]) {
    const orgMembers = await this.memberRepository.find({
      where: {
        id: In(memberIds),
        isVerified: true,
      },
    });

    return Promise.all(
      orgMembers.map(async (member) => ({
        ...member,
        imageUrl: member.imageUrl
          ? await this.storageService.generateSignedUrl(
              member.imageUrl,
              315360000,
            )
          : null,
      })),
    );
  }

  async getVerifiedMemberIds(memberIds: number[]) {
    const verifiedMembers = await this.memberRepository.find({
      where: {
        id: In(memberIds),
        isVerified: true,
      },
      select: ['id'],
    });
    return verifiedMembers.map((m) => m.id);
  }
}
