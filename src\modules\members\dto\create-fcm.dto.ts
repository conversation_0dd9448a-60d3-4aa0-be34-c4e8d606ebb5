import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { DeviceType } from '../entities/member-fcm-token.entity';

export class CreateFcmTokenDto {
  @ApiProperty({
    example: 'fcm_token_12345',
    description: 'Firebase Cloud Messaging token',
  })
  @IsNotEmpty()
  @IsString()
  fcmToken: string;

  @ApiPropertyOptional({ example: 'device-abc-123', description: 'Device ID' })
  @IsOptional()
  @IsString()
  deviceId?: string;

  @ApiPropertyOptional({
    enum: DeviceType,
    example: DeviceType.ANDROID,
    description: 'Type of device',
  })
  @IsOptional()
  @IsEnum(DeviceType)
  deviceType?: DeviceType;

  @ApiPropertyOptional({
    example: '1.0.3',
    description: 'Version of the app installed on device',
  })
  @IsOptional()
  @IsString()
  appVersion?: string;
}
