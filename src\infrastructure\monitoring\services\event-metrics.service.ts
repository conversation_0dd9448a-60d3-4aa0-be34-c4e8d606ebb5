import { Injectable, Logger } from '@nestjs/common';
import { BaseEvent } from '../../../common/events/base.event';
import { Counter, Histogram, Gauge, register } from 'prom-client';

@Injectable()
export class EventMetricsService {
  private readonly logger = new Logger(EventMetricsService.name);

  private readonly eventCounter: Counter<string>;
  private readonly processingHistogram: Histogram<string>;
  private readonly lastProcessedGauge: Gauge<string>;
  private readonly memoryUsageGauge: Gauge<string>;
  private readonly messageCounter: Counter<string>;
  private readonly otpRequestCounter: Counter<string>;
  private readonly otpSendSuccessCounter: Counter<string>;
  private readonly otpSendFailureCounter: Counter<string>;

  constructor() {
    this.eventCounter =
      (register.getSingleMetric('events_total') as Counter<string>) ||
      new Counter({
        name: 'events_total',
        help: 'Total number of events processed',
        labelNames: ['event_name', 'status'],
      });

    this.processingHistogram =
      (register.getSingleMetric(
        'event_processing_duration_ms',
      ) as Histogram<string>) ||
      new Histogram({
        name: 'event_processing_duration_ms',
        help: 'Duration of event processing in ms',
        labelNames: ['event_name'],
        buckets: [50, 100, 300, 500, 1000, 2000, 5000],
      });

    this.lastProcessedGauge =
      (register.getSingleMetric(
        'event_last_processed_timestamp_seconds',
      ) as Gauge<string>) ||
      new Gauge({
        name: 'event_last_processed_timestamp_seconds',
        help: 'Timestamp of last processed event',
        labelNames: ['event_name'],
      });

    this.memoryUsageGauge =
      (register.getSingleMetric(
        'app_memory_usage_percentage',
      ) as Gauge<string>) ||
      new Gauge({
        name: 'app_memory_usage_percentage',
        help: 'Memory usage percentage',
      });

    this.messageCounter =
      (register.getSingleMetric('app_messages_total') as Counter<string>) ||
      new Counter({
        name: 'app_messages_total',
        help: 'Total number of messages processed',
      });

    this.otpRequestCounter =
      (register.getSingleMetric('otp_requests_total') as Counter<string>) ||
      new Counter({
        name: 'otp_requests_total',
        help: 'Total OTP requests received',
      });

    this.otpSendSuccessCounter =
      (register.getSingleMetric('otp_send_success_total') as Counter<string>) ||
      new Counter({
        name: 'otp_send_success_total',
        help: 'Total successful OTP sends',
      });

    this.otpSendFailureCounter =
      (register.getSingleMetric('otp_send_failure_total') as Counter<string>) ||
      new Counter({
        name: 'otp_send_failure_total',
        help: 'Total failed OTP sends',
      });
  }

  incrementOtpRequestCount() {
    this.otpRequestCounter.inc();
  }

  incrementOtpSendSuccess() {
    this.otpSendSuccessCounter.inc();
  }

  incrementOtpSendFailure() {
    this.otpSendFailureCounter.inc();
  }

  incrementMessageCount() {
    this.messageCounter.inc();
  }

  updateMemoryUsage(percentage: number) {
    this.memoryUsageGauge.set(percentage);
  }

  getHealthStatus() {
    const healthy = true;
    const issues = [];
    return { healthy, issues };
  }

  getMetrics() {
    return register.metrics();
  }

  recordEventEmitted(event: BaseEvent): void {
    // Optionally track emitted events if needed; here just update last processed
    this.lastProcessedGauge.set({ event_name: event.name }, Date.now() / 1000);
  }

  recordEventProcessed(
    eventName: string,
    processingTimeMs: number,
    success: boolean,
  ): void {
    // Increment success or failure count
    this.eventCounter.inc({
      event_name: eventName,
      status: success ? 'success' : 'failure',
    });

    // Record processing time for histogram
    this.processingHistogram.observe(
      { event_name: eventName },
      processingTimeMs,
    );

    // Update last processed time gauge
    this.lastProcessedGauge.set({ event_name: eventName }, Date.now() / 1000);
  }
}
