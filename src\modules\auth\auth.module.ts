import { Module } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PassportModule } from '@nestjs/passport';
import { AuthController } from './controllers/auth.controller';
import { AuthService } from './services/auth.service';
import { JwtStrategy } from './strategies/jwt.strategy';
import { RedisModule } from '../../infrastructure/redis/redis.module';
import { OtpVerification } from './entities';
import { MembersModule } from '../members/members.module';
import { OtpController } from './controllers/otp.controller';
import { OtpService } from './services/otp.service';
import { UserModule } from '../users/users.module';
import { OrganizationsModule } from '../organization/organizations.module';
import { CoreModule } from '../../core/core.module';
import { EventModule } from '../../infrastructure/events/event.module';
import { RabbitMQConfigModule } from 'src/infrastructure/rabbitmq/rabbitMq.module';
import { OtpRequestedHandler } from './handlers/otp-requested.handler';
import { TokenService } from './services/token.service';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    TypeOrmModule.forFeature([OtpVerification]),
    RedisModule,
    MembersModule,
    CoreModule,
    UserModule,
    OrganizationsModule,
    EventModule,
    RabbitMQConfigModule,
  ],
  controllers: [AuthController, OtpController],
  providers: [
    AuthService,
    JwtStrategy,
    TokenService,
    JwtService,
    OtpRequestedHandler,
    OtpService,
  ],
})
export class AuthModule {}
