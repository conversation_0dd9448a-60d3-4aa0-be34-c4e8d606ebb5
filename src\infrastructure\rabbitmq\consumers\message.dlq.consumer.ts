import { Injectable, Logger } from '@nestjs/common';
import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { MESSAGE_DLQ_QUEUE, MESSAGE_DLQ_EXCHANGE } from 'src/common/constants';
import { EventBusService } from 'src/infrastructure/events/event-bus.service';
import { MessageDeliveryFailedEvent } from 'src/common/events/message.events';

@Injectable()
export class MessageDlqConsumer {
  private readonly logger = new Logger(MessageDlqConsumer.name);

  constructor(private readonly eventBus: EventBusService) {}

  @RabbitSubscribe({
    exchange: MESSAGE_DLQ_EXCHANGE,
    routingKey: '#',
    queue: MESSAGE_DLQ_QUEUE,
    queueOptions: { durable: true },
  })
  async handleDlqMessage(message: {
    messageId: number;
    memberIds?: number[];
    messageData?: any;
    retryCount?: number;
    failureReason?: string;
  }) {
    this.logger.warn(
      `DLQ received for message ${message.messageId}, reason: ${message.failureReason}`,
    );

    this.eventBus.publish(
      new MessageDeliveryFailedEvent(
        message.messageId,
        message.failureReason || 'DLQ reached',
      ),
    );

    // Optional: store DLQ message in DB for delta sync or manual retry
  }
}
