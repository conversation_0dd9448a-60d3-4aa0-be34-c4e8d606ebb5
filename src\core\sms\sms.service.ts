import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosError } from 'axios';

export class SmsLimitExceededError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'SmsLimitExceededError';
  }
}

export class SmsAuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'SmsAuthenticationError';
  }
}

export class SmsTemporaryError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'SmsTemporaryError';
  }
}

export class SmsShouldGoToDlqError extends Error {
  constructor(
    message: string,
    public originalError?: any,
  ) {
    super(message);
    this.name = 'SmsShouldGoToDlqError';
  }
}

@Injectable()
export class SmsService {
  private readonly logger = new Logger(SmsService.name);

  constructor(private readonly configService: ConfigService) {}

  /** Send SMS Verification */
  async sendSmsVerification(mobileNumber: string, otp: string): Promise<void> {
    const message = `Your OTP: ${otp}. Use this to verify your account.`;

    try {
      const response = await axios.post(
        this.configService.get<string>('TEXT_LK_API_URL') || '',
        {
          recipient: mobileNumber,
          sender_id: this.configService.get<string>('TEXT_LK_SENDER_ID'),
          type: 'plain',
          message,
        },
        {
          headers: {
            Authorization: `Bearer ${this.configService.get<string>('TEXT_LK_API_KEY')}`,
            'Content-Type': 'application/json',
            Accept: 'application/json',
          },
        },
      );

      this.logger.log(`SMS sent successfully to ${mobileNumber}`);
    } catch (error) {
      this.handleSmsError(error as AxiosError, mobileNumber);
    }
  }

  private handleSmsError(error: AxiosError, phoneNumber: string): never {
    const status = error.response?.status;
    const responseData = error.response?.data as any;
    const errorMessage = responseData?.message || error.message;

    // Log the error details
    this.logger.error(`SMS API Error for ${phoneNumber}:`, {
      status,
      statusText: error.response?.statusText,
      message: errorMessage,
      data: responseData,
    });

    // Handle specific error types
    if (
      responseData?.message &&
      responseData.message.toLowerCase().includes('exceeded') &&
      responseData.message.toLowerCase().includes('limit')
    ) {
      throw new SmsShouldGoToDlqError(
        `SMS sending limit exceeded: ${errorMessage}`,
      );
    }

    switch (status) {
      case 401:
      case 403:
        throw new SmsAuthenticationError(
          `SMS authentication failed: ${errorMessage}`,
        );

      case 400:
      case 404:
      case 422:
        throw new SmsShouldGoToDlqError(`SMS permanent error: ${errorMessage}`);

      case 429:
        throw new SmsLimitExceededError(
          `SMS rate limit exceeded: ${errorMessage}`,
        );

      case 500:
      case 502:
      case 503:
      case 504:
        throw new SmsTemporaryError(
          `SMS service temporarily unavailable: ${errorMessage}`,
        );

      default:
        // For unknown errors, treat as temporary to allow retry
        throw new SmsTemporaryError(`SMS service error: ${errorMessage}`);
    }
  }
}
