import { Inject, Injectable, Logger } from '@nestjs/common';
import { FcmService } from './fcm.service';
import { ConfigService } from '@nestjs/config';
import { CacheService } from 'src/infrastructure/redis/services/cache.service';
import { ISocketRoomService } from '../../socket/interfaces/socket-room.interface';
import { IChatRoomValidator } from 'src/infrastructure/socket/interfaces/chat-room-validator.interface';
import { PRIORITY } from '../constants/notification.constants';

export interface PushNotification {
  title: string;
  body: string;
  data: Record<string, any>;
  imageUrl?: string;
}

export interface SendPushNotificationDto {
  memberIds: number[];
  notification: PushNotification;
  priority?: PRIORITY;
  silent?: boolean;
}

export interface MemberNotificationInfo {
  memberId: number;
  silent: boolean;
}

@Injectable()
export class NotificationService {
  private readonly logger = new Logger(NotificationService.name);
  private readonly ackTimeouts = new Map<string, NodeJS.Timeout>();

  constructor(
    @Inject('ISocketRoomService')
    private readonly socketService: ISocketRoomService,
    @Inject('IChatRoomValidator')
    private readonly roomValidationService: IChatRoomValidator,
    private readonly fcmService: FcmService,
    private readonly cacheService: CacheService,
    private readonly configService: ConfigService,
  ) {}

  async broadcastToMemberWithAck(
    memberId: number,
    eventName: string,
    payload: any,
  ): Promise<void> {
    const ackTimeout = this.configService.get<number>(
      'GROUP_ACK_TIMEOUT',
      5000,
    );
    const eventTimestamp = Date.now();
    const ackKey = `${eventName}:${memberId}:${eventTimestamp}`;

    try {
      // 1. Send via socket with metadata for acknowledgement
      this.socketService.broadcastToMember(memberId, eventName, {
        ...payload,
        _ackRequired: true,
        _eventTimestamp: eventTimestamp,
      });

      // 2. Set up acknowledgement timeout
      this.ackTimeouts.set(
        ackKey,
        setTimeout(async () => {
          this.logger.warn(`No ACK received for ${eventName} from ${memberId}`);
          await this.sendFallbackNotification(memberId, eventName, payload);
          this.ackTimeouts.delete(ackKey);
        }, ackTimeout),
      );
    } catch (socketError) {
      this.logger.error(`Socket broadcast failed to ${memberId}`, socketError);
      await this.sendFallbackNotification(memberId, eventName, payload);
    }
  }

  async broadcastToMembersWithAck(
    memberIds: number[],
    eventName: string,
    payload: any,
  ): Promise<void> {
    await Promise.all(
      memberIds.map((memberId) =>
        this.broadcastToMemberWithAck(memberId, eventName, payload),
      ),
    );
  }

  /**
   * Broadcast group message with room validation and push notifications
   */
  async broadcastGroupMessageWithValidation(
    groupId: number,
    memberNotificationInfo: MemberNotificationInfo[],
    eventName: string,
    payload: any,
    notification: PushNotification,
  ): Promise<void> {
    const connectedMembers: number[] = [];
    const disconnectedMembers: MemberNotificationInfo[] = [];

    for (const memberInfo of memberNotificationInfo) {
      const isInRoom = await this.roomValidationService.validateMemberInGroup(
        memberInfo.memberId,
        groupId,
      );

      if (isInRoom) {
        connectedMembers.push(memberInfo.memberId);
      } else {
        disconnectedMembers.push(memberInfo);
      }
    }

    this.logger.debug(
      `Group ${groupId} - Connected: ${connectedMembers.length}, Disconnected: ${disconnectedMembers.length}`,
    );

    // ✅ Send socket messages
    if (connectedMembers.length > 0) {
      await this.broadcastToMembersWithAck(
        connectedMembers,
        eventName,
        payload,
      );
    }

    // ✅ Send push/silent notifications individually
    for (const memberInfo of disconnectedMembers) {
      await this.sendPushNotification({
        memberIds: [memberInfo.memberId],
        notification,
        priority: PRIORITY.HIGH,
        silent: memberInfo.silent,
      });
    }
  }

  /**
   * Broadcast private chat message with room validation and push notifications
   */
  async broadcastPrivateChatMessageWithValidation(
    senderId: number,
    receiverId: number,
    eventName: string,
    payload: any,
    notification: PushNotification,
  ): Promise<void> {
    // Check if receiver is in the private chat room
    const isReceiverInRoom =
      await this.roomValidationService.validateMemberInPrivateChat(
        receiverId,
        senderId,
      );

    if (isReceiverInRoom) {
      // Send socket message
      await this.broadcastToMemberWithAck(receiverId, eventName, payload);
    } else {
      // Send push notification
      await this.sendPushNotification({
        memberIds: [receiverId],
        notification,
        priority: PRIORITY.HIGH,
      });
    }
  }

  /**
   * Send push notifications to multiple members
   */
  async sendPushNotification(dto: SendPushNotificationDto): Promise<void> {
    try {
      const allTokens: string[] = [];
      const memberTokenMap = new Map<number, string[]>();

      // Collect FCM tokens for all members
      for (const memberId of dto.memberIds) {
        const tokens = await this.cacheService.getFcmTokens(memberId);
        if (tokens?.length) {
          memberTokenMap.set(memberId, tokens);
          allTokens.push(...tokens);
        } else {
          this.logger.warn(`No FCM tokens found for member ${memberId}`);
        }
      }

      if (allTokens.length === 0) {
        this.logger.warn('No FCM tokens available for push notification');
        return;
      }

      // Send notification
      if (dto.silent) {
        await this.fcmService.sendSilentNotification({
          tokens: allTokens,
          data: {
            ...dto.notification.data,
            title: dto.notification.title,
            body: dto.notification.body,
            imageUrl: dto.notification.imageUrl || '',
            timestamp: Date.now().toString(),
          },
        });
      } else {
        await this.fcmService.sendNotification({
          tokens: allTokens,
          title: dto.notification.title,
          body: dto.notification.body,
          imageUrl: dto.notification.imageUrl,
          data: dto.notification.data || {},
          priority: dto.priority || PRIORITY.NORMAL,
        });
      }

      this.logger.log(
        `Push notification sent to ${allTokens.length} tokens for ${dto.memberIds.length} members`,
      );
    } catch (error) {
      this.logger.error('Failed to send push notification:', error);
      throw error;
    }
  }

  /**
   * Send push notification to a single member
   */
  async sendPushNotificationToMember(
    memberId: number,
    notification: PushNotification,
    priority: PRIORITY = PRIORITY.NORMAL,
    silent: boolean = false,
  ): Promise<void> {
    await this.sendPushNotification({
      memberIds: [memberId],
      notification,
      priority,
      silent,
    });
  }

  handleAcknowledgement(
    memberId: number,
    eventName: string,
    eventTimestamp: number,
  ): void {
    const ackKey = `${eventName}:${memberId}:${eventTimestamp}`;
    const timeout = this.ackTimeouts.get(ackKey);

    if (timeout) {
      clearTimeout(timeout);
      this.ackTimeouts.delete(ackKey);
      this.logger.debug(`ACK received for ${eventName} from ${memberId}`);
    }
  }

  private async sendFallbackNotification(
    memberId: number,
    eventName: string,
    payload: any,
  ): Promise<void> {
    try {
      const fcmTokens = await this.cacheService.getFcmTokens(memberId);
      if (!fcmTokens?.length) {
        this.logger.warn(`No FCM tokens for ${memberId}`);
        return;
      }

      await this.fcmService.sendSilentNotification({
        tokens: fcmTokens,
        data: {
          event: eventName,
          payload: JSON.stringify(payload),
          group_id: payload.groupId || payload.id,
          timestamp: Date.now().toString(),
        },
      });
    } catch (error) {
      this.logger.error(`FCM fallback failed for ${memberId}`, error);
    }
  }

  cleanupPendingTimeouts(): void {
    this.ackTimeouts.forEach((timeout, key) => {
      clearTimeout(timeout);
      this.ackTimeouts.delete(key);
    });
  }
}
