import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  ParseIntPipe,
  Put,
  Res,
  Req,
  UnauthorizedException,
  UseInterceptors,
} from '@nestjs/common';
import { OrganizationsService } from '../services/organizations.service';
import { CreateOrganizationDto, UpdateOrganizationDto } from '../dto';
import { Organization } from '../entities/organization.entity';
import { Response, Request } from 'express';
import { Public } from '../../../common/decorators/public.decorator';
import { ImageUrlInterceptor } from '../../../common/interceptors/image-url.interceptor';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
} from '@nestjs/swagger';

import {
  AdminOnly,
  ProductAdminOnly,
} from 'src/common/decorators/roles.decorator';

@ApiTags('Organizations') // Group in Swagger
@ApiBearerAuth() // Requires JWT unless marked @Public
@Controller('organizations')
export class OrganizationsController {
  constructor(private readonly organizationsService: OrganizationsService) {}

  @Post('create-org')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Create a new organization (Admin only)' })
  @ApiBody({ type: CreateOrganizationDto })
  @ApiResponse({
    status: 200,
    description: 'Organization created successfully',
    type: Organization,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Only admins allowed',
  })
  @ProductAdminOnly()
  create(
    @Body() createOrganizationDto: CreateOrganizationDto,
    @Res({ passthrough: true }) response: Response,
  ): Promise<Organization> {
    response.locals.message = 'Organization created successfully';
    return this.organizationsService.create(createOrganizationDto);
  }

  @Public()
  @Get(':id')
  @UseInterceptors(ImageUrlInterceptor)
  @ApiOperation({ summary: 'Get organization details by ID (Public)' })
  @ApiParam({ name: 'id', type: Number, description: 'Organization ID' })
  @ApiResponse({
    status: 200,
    description: 'Organization fetched successfully',
    type: Organization,
  })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  findOne(
    @Param('id', ParseIntPipe) id: number,
    @Res({ passthrough: true }) response: Response,
  ): Promise<Organization> {
    response.locals.message = 'Organization fetched successfully';
    return this.organizationsService.findOne(id);
  }

  @Get()
  @ProductAdminOnly()
  @UseInterceptors(ImageUrlInterceptor)
  @ApiOperation({ summary: 'Get all organizations (Admin only)' })
  @ApiResponse({
    status: 200,
    description: 'Organizations fetched successfully',
    type: [Organization],
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Only admins allowed',
  })
  findAll(@Res({ passthrough: true }) response: Response) {
    response.locals.message = 'Organizations fetched successfully';
    return this.organizationsService.findAll();
  }

  @Put(':id')
  @ApiOperation({ summary: 'Update organization details (Admin only)' })
  @ApiParam({ name: 'id', type: Number, description: 'Organization ID' })
  @ApiBody({ type: UpdateOrganizationDto })
  @ApiResponse({
    status: 200,
    description: 'Organization updated successfully',
    type: Organization,
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Only admins allowed',
  })
  @ApiResponse({ status: 404, description: 'Organization not found' })
  @AdminOnly()
  update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @Res({ passthrough: true }) response: Response,
  ): Promise<Organization> {
    response.locals.message = 'Organization updated successfully';
    return this.organizationsService.update(id, updateOrganizationDto);
  }
}
