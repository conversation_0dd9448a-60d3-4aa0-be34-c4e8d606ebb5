import { SetMetadata } from '@nestjs/common';
import { UserType } from '../types/user-type';
import { USER_TYPE } from '../constants/user-type.constants';

export const ROLES_KEY = 'allowedUserTypes';
export const AllowedUserTypes = (...userTypes: UserType[]) =>
  SetMetadata(ROLES_KEY, userTypes);

// Alternative specific decorators for common use cases
export const AdminOnly = () =>
  AllowedUserTypes(USER_TYPE.PRODUCT_ADMIN, USER_TYPE.ORG_ADMIN);
export const ProductAdminOnly = () => AllowedUserTypes(USER_TYPE.PRODUCT_ADMIN);
export const OrgAdminOnly = () => AllowedUserTypes(USER_TYPE.ORG_ADMIN);
export const MemberOnly = () => AllowedUserTypes(USER_TYPE.ORG_MEMBER);
export const AdminsAndMembers = () =>
  AllowedUserTypes(
    USER_TYPE.PRODUCT_ADMIN,
    USER_TYPE.ORG_ADMIN,
    USER_TYPE.ORG_MEMBER,
  );
