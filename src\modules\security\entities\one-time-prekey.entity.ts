// src/modules/security/entities/one-time-prekey.entity.ts
import { OrgMember } from '../../members/entities/org-member.entity';
import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  Index,
  ManyToOne,
  JoinColumn,
} from 'typeorm';

@Entity('one_time_prekeys')
export class OneTimePreKeyEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column()
  @Index()
  memberId: string;

  @Column()
  keyId: number;

  @Column('text')
  publicKey: string;

  @Column({ default: false })
  isUsed: boolean;

  @CreateDateColumn({ type: 'timestamp' })
  createdAt: Date;

  @ManyToOne(() => OrgMember)
  @JoinColumn({ name: 'memberId' }) // or rename to memberId
  member: OrgMember;
}
