import {
  IsNumber,
  IsString,
  IsOptional,
  IsNotEmpty,
  IsObject,
} from 'class-validator';
import { ChatOperationDto } from 'src/infrastructure/socket/dto/chat-operation.dto';

export class SendPrivateMessageDto extends ChatOperationDto {
  @IsNumber()
  senderId: number;

  @IsNumber()
  receiverId: number;

  @IsString()
  @IsNotEmpty()
  encryptedContent: string;

  @IsString()
  @IsNotEmpty()
  nonce: string;

  @IsObject()
  @IsOptional()
  encryptedMetaData?: Record<string, any>;

  @IsNumber()
  @IsOptional()
  replyToMessageId?: number;

  @IsNumber()
  @IsOptional()
  fileId?: number;

  @IsOptional()
  sentAt?: Date;

  @IsString()
  @IsOptional()
  ephemeralPublicKey?: string;

  @IsNumber()
  @IsOptional()
  previousChainLength?: number;

  @IsOptional()
  @IsNumber()
  messageIndex?: number;

  @IsNumber()
  @IsOptional()
  chainKeyVersion?: number;

  @IsOptional()
  queryRunner?: any;

  @IsNumber()
  @IsOptional()
  seq?: number;
  content: any;
  metadata: any;
}
