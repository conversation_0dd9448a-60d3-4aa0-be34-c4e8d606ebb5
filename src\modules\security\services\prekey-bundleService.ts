// src/modules/security/services/prekey-bundle.service.ts
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MoreThan, Repository } from 'typeorm';
import { SignedPreKeyEntity } from '../entities/signed-prekey.entity';
import { OneTimePreKeyEntity } from '../entities/one-time-prekey.entity';
import { CreateSignedPreKeyDto } from '../dtos/create-signed-prekey.dto';
import { identity } from 'rxjs';
import { OrgMember } from 'src/modules/members/entities/org-member.entity';
import { MemberResponseDto } from 'src/modules/members/dto/member-response.dto';

@Injectable()
export class PreKeyBundleService {
  constructor(
    @InjectRepository(SignedPreKeyEntity)
    private readonly signedPreKeyRepo: Repository<SignedPreKeyEntity>,

    @InjectRepository(OneTimePreKeyEntity)
    private readonly oneTimePreKeyRepo: Repository<OneTimePreKeyEntity>,
  ) {}

  async createPreKeyBundle(memberId: string, dto: CreateSignedPreKeyDto) {
    // 1. Save the signed prekey
    const signedPreKey = this.signedPreKeyRepo.create({
      memberId,
      keyId: dto.keyId,
      publicKey: dto.publicKey,
      signature: dto.signature,
      expireAt: dto.expireAt,
    });
    await this.signedPreKeyRepo.save(signedPreKey);

    // 2. Save the provided one-time prekeys
    const oneTimePreKeys = dto.oneTimePreKeys.map((k) =>
      this.oneTimePreKeyRepo.create({
        memberId,
        keyId: k.keyId,
        publicKey: k.publicKey,
        isUsed: false,
      }),
    );
    await this.oneTimePreKeyRepo.save(oneTimePreKeys);

    // 3. Return the full prekey bundle
    return {
      signedPreKeyId: signedPreKey.id,
      keyId: signedPreKey.keyId,
      publicKey: signedPreKey.publicKey,
      signature: signedPreKey.signature,
      expireAt: signedPreKey.expireAt,
      oneTimePreKeys: oneTimePreKeys.map((k) => ({
        keyId: k.keyId,
        publicKey: k.publicKey,
      })),
    };
  }

  async findAllSignedPreKeys(memberId: string) {
    return this.signedPreKeyRepo.find({ where: { memberId } });
  }

  async getPreKeyBundle(memberId: string) {
    const now = new Date();

    // 1. Get latest unexpired signed prekey
    const signedPreKey = await this.signedPreKeyRepo.findOne({
      where: { memberId, expireAt: MoreThan(now) },
      order: { createdAt: 'DESC' },
    });

    if (!signedPreKey) {
      throw new NotFoundException('No valid (unexpired) signed prekey found');
    }

    // 2. Get one unused one-time prekey
    const oneTimePreKey = await this.oneTimePreKeyRepo.findOne({
      where: { memberId, isUsed: false },
      order: { createdAt: 'ASC' },
    });

    if (!oneTimePreKey) {
      throw new NotFoundException('No one-time prekeys available');
    }

    // 3. Mark one-time prekey as used
    oneTimePreKey.isUsed = true;
    await this.oneTimePreKeyRepo.save(oneTimePreKey);

    // 4. Fetch member with identityKey relation
    const member = await this.signedPreKeyRepo.manager.findOne(OrgMember, {
      where: { id: Number(memberId) },
      relations: ['identityKey'],
    });

    if (!member || !member.identityKey?.publicKey) {
      throw new NotFoundException('Member identity public key not found');
    }

    // 5. Return the bundle
    return {
      identityKey: member.identityKey.publicKey,
      signedPreKey: {
        keyId: signedPreKey.keyId,
        publicKey: signedPreKey.publicKey,
        signature: signedPreKey.signature,
        expireAt: signedPreKey.expireAt,
      },
      oneTimePreKey: {
        keyId: oneTimePreKey.keyId,
        publicKey: oneTimePreKey.publicKey,
      },
    };
  }
}
