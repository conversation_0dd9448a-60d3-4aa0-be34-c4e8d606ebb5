import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { <PERSON><PERSON>tyManager, <PERSON>Than, Repository } from 'typeorm';
import {
  GroupChangeType,
  GroupLogAction,
} from '../enums/group-log-action.enum';
import { GroupChangeLog } from '../entities/group-change-logs.entity';

@Injectable()
export class GroupLogService {
  constructor(
    @InjectRepository(GroupChangeLog)
    private readonly groupLogRepo: Repository<GroupChangeLog>,
  ) {}

  // 🔹 For group join/leave/key changes
  async logChange(
    groupId: number,
    changeType: GroupChangeType,
    action: GroupLogAction,
    options?: {
      memberId?: number;
      changedField?: string;
      oldValue?: string;
      newValue?: string;
    },
  ) {
    const maxSeq = await this.groupLogRepo
      .createQueryBuilder('log')
      .select('MAX(log.seq)', 'maxSeq')
      .where('log.group_id = :groupId', { groupId })
      .getRawOne();

    const nextSeq = (parseInt(maxSeq?.maxSeq, 10) || 0) + 1;

    const logEntry = this.groupLogRepo.create({
      seq: nextSeq,
      group: { id: groupId },
      changeType,
      action,
      memberId: options?.memberId,
      changedField: options?.changedField,
      oldValue: options?.oldValue,
      newValue: options?.newValue,
    });

    await this.groupLogRepo.save(logEntry);
    return logEntry;
  }

  // 🔹 For group changes
  async getChangesFromSeq(
    groupId: number,
    changeType: GroupChangeType,
    fromSeq: number,
    memberId?: number,
  ) {
    const hasSequence = await this.groupLogRepo.count({
      where: { seq: fromSeq },
    });
    if (!hasSequence) {
      throw new Error('Sequence data not found');
    }

    const whereClause: any = {
      group: { id: groupId },
      changeType,
      seq: MoreThan(fromSeq),
    };
    if (memberId) {
      whereClause.memberId = memberId;
    }

    return this.groupLogRepo.find({
      where: whereClause,
      order: { seq: 'ASC' },
    });
  }

  // 🔹 For profile changes (no groupId required)
  async getProfileChangesFromSeq(memberId: number, fromSeq: number) {
    const hasSequence = await this.groupLogRepo.count({
      where: { seq: fromSeq },
    });
    if (!hasSequence) {
      throw new Error('Sequence data not found');
    }

    return this.groupLogRepo.find({
      where: {
        memberId,
        changeType: GroupChangeType.PROFILE,
        seq: MoreThan(fromSeq),
      },
      order: { seq: 'ASC' },
    });
  }

  async getLatestLog(groupId: number): Promise<GroupChangeLog | null> {
    return this.groupLogRepo.findOne({
      where: { group: { id: groupId } },
      order: { seq: 'DESC' },
    });
  }

  // 🔹 For logging member profile updates
  async logMemberUpdate(
    memberId: number,
    changedField: string,
    oldValue: string,
    newValue: string,
    manager?: EntityManager,
  ) {
    const repo = manager
      ? manager.getRepository(GroupChangeLog)
      : this.groupLogRepo;

    const maxSeq = await repo
      .createQueryBuilder('log')
      .select('MAX(log.seq)', 'maxSeq')
      .where('log.memberId = :memberId', { memberId })
      .getRawOne();

    const nextSeq = (parseInt(maxSeq?.maxSeq, 10) || 0) + 1;

    const logEntry = repo.create({
      seq: nextSeq,
      changeType: GroupChangeType.PROFILE,
      action: GroupLogAction.PROFILE_UPDATE,
      memberId,
      changedField,
      oldValue,
      newValue,
    });

    return repo.save(logEntry);
  }
}
