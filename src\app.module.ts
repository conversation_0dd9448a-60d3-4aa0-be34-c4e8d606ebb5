import { Modu<PERSON>, OnApplicationBootstrap } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  databaseConfig,
  jwtConfig,
  redisConfig,
  storageConfig,
  rabbitmqConfig,
  appConfig,
  smsConfig,
  circuitBreakerConfig,
  sagaConfig,
} from './config';
import { CoreModule } from './core/core.module';
import { InfrastructureModule } from './infrastructure/infrastructure.module';
import { FeatureModule } from './modules/feature.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductAdminSeeder } from './infrastructure/database/seeders/initial-data.seed';
import { JwtModule, JwtService } from '@nestjs/jwt';
import { mailConfig } from './config/mail.config';
import { APP_GUARD } from '@nestjs/core';
import { JwtAuthGuard } from './common/guards/jwt-auth.guard';
import { ScheduleModule } from '@nestjs/schedule';
import { ThrottlerModule } from '@nestjs/throttler';
import { RedisModule } from './infrastructure/redis/redis.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { RolesGuard } from './common/guards/roles.guard';
import { AdvancedThrottlerGuard } from './common/guards/rate-limit.guard';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [
        databaseConfig,
        jwtConfig,
        redisConfig,
        storageConfig,
        rabbitmqConfig,
        appConfig,
        smsConfig,
        mailConfig,
        circuitBreakerConfig,
        sagaConfig,
      ],
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => {
        const databaseConfig = configService.get('database');
        if (!databaseConfig) {
          throw new Error('Database configuration is missing');
        }
        return databaseConfig;
      },
      inject: [ConfigService],
    }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) => ({
        secret: configService.get<string>('jwt.secret'),
        signOptions: {
          expiresIn: configService.get<string>('jwt.expiresIn', '1d'),
        },
      }),
    }),
    EventEmitterModule.forRoot({
      wildcard: false,
      delimiter: '.',
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    ScheduleModule.forRoot(),
    RedisModule.forRoot(),

    CoreModule,
    InfrastructureModule,
    FeatureModule,
  ],
  controllers: [AppController],
  providers: [
    AppService,
    ProductAdminSeeder,
    JwtService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: RolesGuard,
    },
    {
      provide: APP_GUARD,
      useClass: AdvancedThrottlerGuard,
    },
  ],
})
export class AppModule implements OnApplicationBootstrap {
  constructor(private readonly seeder: ProductAdminSeeder) {}

  async onApplicationBootstrap() {
    try {
      await this.seeder.seed();
      console.log('Database seeding completed successfully');
    } catch (error) {
      console.error('Error during database initialization:', error);
      throw error;
    }
  }
}
