import {
  <PERSON>umn,
  CreateDate<PERSON><PERSON>umn,
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  OneToOne,
  JoinColumn,
} from 'typeorm';
import { SyncState } from './sync-state.entity';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';

@Entity('message_threads')
export class MessageThread {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'member_id' })
  memberId: number;

  @Column({ name: 'chat_type' })
  chatType: ChatType;

  @Column({ name: 'target_id' })
  targetId: number;

  @Column({ name: 'last_message_id', nullable: true })
  lastMessageId: number;

  @Column({ name: 'unread_count', default: 0 })
  unreadCount: number;

  @Column({ name: 'is_muted', default: false })
  isMuted: boolean;

  @Column({ name: 'is_pinned', default: false })
  isPinned: boolean;

  @Column({ name: 'welcome_message', nullable: true })
  welcomeMessage: string;

  @UpdateDateColumn({ name: 'updated_at', type: 'timestamp' })
  updatedAt: Date;

  @CreateDateColumn({ name: 'created_at', type: 'timestamp' })
  createdAt: Date;

  // 🔹 Link with sync state
  @OneToOne(() => SyncState, (sync) => sync.thread, { cascade: true })
  @JoinColumn({ name: 'sync_state_id' })
  syncState: SyncState;
}
