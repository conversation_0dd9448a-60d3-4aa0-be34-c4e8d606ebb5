import {
  IsBoolean,
  IsN<PERSON>ber,
  IsOptional,
  IsPositive,
  Min,
  IsEnum,
} from 'class-validator';

export class CreateSyncStateDto {
  @IsNumber()
  @IsOptional()
  @Min(0)
  lastSyncedSeq?: number = 0;

  @IsNumber()
  @IsOptional()
  @Min(0)
  lastAckedSeq?: number = 0;

  @IsBoolean()
  @IsOptional()
  hasPending?: boolean = false;
}

export class UpdateSyncStateDto {
  @IsNumber()
  @IsOptional()
  @Min(0)
  lastSyncedSeq?: number;

  @IsNumber()
  @IsOptional()
  @Min(0)
  lastAckedSeq?: number;

  @IsBoolean()
  @IsOptional()
  hasPending?: boolean;
}

export class UpdateLastSyncedSeqDto {
  @IsNumber()
  @IsPositive()
  threadId: number;

  @IsNumber()
  @Min(0)
  seq: number;
}

export class UpdateLastAckedSeqDto {
  @IsNumber()
  @IsPositive()
  threadId: number;

  @IsNumber()
  @Min(0)
  seq: number;
}

export class MarkPendingDto {
  @IsNumber()
  @IsPositive()
  threadId: number;
}

export class ClearPendingDto {
  @IsNumber()
  @IsPositive()
  threadId: number;
}

export enum ChatType {
  GROUP = 'group',
  PRIVATE = 'private',
}

export class GetSyncStateDto {
  @IsNumber()
  @IsPositive()
  userId: number;

  @IsEnum(ChatType)
  chatType: ChatType;

  @IsNumber()
  @IsPositive()
  targetId: number;
}

export class BulkUpdateSyncStatesDto {
  @IsNumber({}, { each: true })
  @IsPositive({ each: true })
  userIds: number[];

  @IsEnum(ChatType)
  chatType: ChatType;

  @IsNumber()
  @IsPositive()
  targetId: number;

  @IsNumber()
  @Min(0)
  newSeq: number;

  @IsNumber()
  @IsPositive()
  senderId: number;
}

export class GetUnsyncedCountDto {
  @IsNumber()
  @IsPositive()
  userId: number;

  @IsEnum(ChatType)
  chatType: ChatType;

  @IsNumber()
  @IsPositive()
  targetId: number;
}

export class ResetSyncStateDto {
  @IsNumber()
  @IsPositive()
  threadId: number;
}

export class GetSyncStatisticsDto {
  @IsNumber()
  @IsPositive()
  userId: number;
}
