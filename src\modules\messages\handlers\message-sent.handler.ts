import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';
import { EVENT_NAMES } from 'src/common/constants/event-names';
import { MessageSentEvent } from 'src/common/events';
import { MessageProducer } from 'src/infrastructure/rabbitmq/producers/message.producer';

// handlers/message-sent.handler.ts
@Injectable()
export class MessageSentHandler {
  private readonly logger = new Logger(MessageSentHandler.name);

  constructor(private readonly messageProducer: MessageProducer) {}

  @OnEvent(EVENT_NAMES.MESSAGE_SENT, { async: true })
  async handle(event: MessageSentEvent) {
    try {
      await this.messageProducer.sendMessageDeliveryTask({
        messageId: event.messageId,
        memberNotificationInfo: event.memberNotificationInfo,
        notificationPayload: event.notificationPayload,
        retryCount: event.retryCount,
      });
    } catch (error) {
      this.logger.warn(
        `Socket broadcast failed, queuing for RabbitMQ delivery`,
      );
    }
  }
}
