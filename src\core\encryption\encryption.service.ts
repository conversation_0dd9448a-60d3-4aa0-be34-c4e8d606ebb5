import { Injectable, Logger } from '@nestjs/common';
import * as nacl from 'tweetnacl';
import * as naclUtil from 'tweetnacl-util';
import * as crypto from 'crypto';
import { EncryptedPrivateKey } from 'src/modules/security/entities/identity-key.entity';

@Injectable()
export class EncryptionService {
  private readonly logger = new Logger(EncryptionService.name);
  /**
   * Generate a new key pair for a user
   * Returns X25519 key pair for key agreement
   */
  generateKeyPair(): { publicKey: string; secretKey: string } {
    const keyPair = nacl.box.keyPair();
    return {
      publicKey: naclUtil.encodeBase64(keyPair.publicKey),
      secretKey: naclUtil.encodeBase64(keyPair.secretKey),
    };
  }

  /**
   * Generate a signing key pair for message authentication
   */
  generateSigningKeyPair(): { publicKey: string; secretKey: string } {
    const keyPair = nacl.sign.keyPair();
    return {
      publicKey: naclUtil.encodeBase64(keyPair.publicKey),
      secretKey: naclUtil.encodeBase64(keyPair.secretKey),
    };
  }

  /**
   * Encrypt a user's secret keys with their password
   * Uses Argon2id via Node.js crypto (recent versions support it)
   */
  encryptSecretKey(secretKey: string, password: string): EncryptedPrivateKey {
    const salt = crypto.randomBytes(16);
    // Use Argon2id when available in your Node.js version, otherwise fallback to scrypt
    let key;
    try {
      // For Node.js 16.6.0+ that supports Argon2id
      key = crypto.scryptSync(password, salt, 32, {
        cost: 16384, // N
        blockSize: 8, // r
        parallelization: 1, // p
        maxmem: 32 * 1024 * 1024, // 32 MB
      });
    } catch (e) {
      // Fallback to standard scrypt
      key = crypto.scryptSync(password, salt, 32);
    }

    const nonce = nacl.randomBytes(nacl.secretbox.nonceLength);
    const messageUint8 = naclUtil.decodeBase64(secretKey);

    const encryptedMessage = nacl.secretbox(messageUint8, nonce, key);

    // Store salt, nonce, and encrypted data
    return {
      salt: naclUtil.encodeBase64(salt),
      nonce: naclUtil.encodeBase64(nonce),
      encryptedData: naclUtil.encodeBase64(encryptedMessage),
      // algorithm: 'nacl.secretbox-xsalsa20-poly1305',
    };
  }

  /**
   * Decrypt a user's secret key with their password
   */
  decryptSecretKey(payload: EncryptedPrivateKey, password: string): string {
    const { salt, nonce, encryptedData } = payload;

    // Use same key derivation as in encrypt
    const key = crypto.scryptSync(password, naclUtil.decodeBase64(salt), 32, {
      cost: 16384,
      blockSize: 8,
      parallelization: 1,
      maxmem: 32 * 1024 * 1024,
    });

    const decrypted = nacl.secretbox.open(
      naclUtil.decodeBase64(encryptedData),
      naclUtil.decodeBase64(nonce),
      key,
    );

    if (!decrypted) {
      throw new Error(
        'Decryption failed. Incorrect password or corrupted data.',
      );
    }

    return naclUtil.encodeBase64(decrypted);
  }

  /**
   * Generate a random symmetric key for a group
   */
  generateGroupKey(): string {
    return naclUtil.encodeBase64(nacl.randomBytes(nacl.secretbox.keyLength));
  }

  /**
   * Encrypt a group key for a specific user
   * Using X25519 key agreement
   */
  encryptGroupKeyForUser(
    groupKey: string,
    recipientPublicKey: string,
    senderSecretKey: string,
  ): string {
    const nonce = nacl.randomBytes(nacl.box.nonceLength);

    const encryptedGroupKey = nacl.box(
      naclUtil.decodeBase64(groupKey),
      nonce,
      naclUtil.decodeBase64(recipientPublicKey),
      naclUtil.decodeBase64(senderSecretKey),
    );

    return JSON.stringify({
      nonce: naclUtil.encodeBase64(nonce),
      encryptedData: naclUtil.encodeBase64(encryptedGroupKey),
    });
  }

  /**
   * Decrypt a group key with a user's secret key
   */
  decryptGroupKey(
    encryptedGroupKey: string,
    senderPublicKey: string,
    recipientSecretKey: string,
  ): string {
    const { nonce, encryptedData } = JSON.parse(encryptedGroupKey);

    const decrypted = nacl.box.open(
      naclUtil.decodeBase64(encryptedData),
      naclUtil.decodeBase64(nonce),
      naclUtil.decodeBase64(senderPublicKey),
      naclUtil.decodeBase64(recipientSecretKey),
    );

    if (!decrypted) {
      throw new Error('Failed to decrypt group key');
    }

    return naclUtil.encodeBase64(decrypted);
  }

  /**
   * Encrypt a message with a group key
   * Uses NaCl secretbox (XSalsa20-Poly1305)
   */
  encryptMessage(message: string, groupKey: string): string {
    const nonce = nacl.randomBytes(nacl.secretbox.nonceLength);
    const messageUint8 = naclUtil.decodeUTF8(message);

    const encryptedMessage = nacl.secretbox(
      messageUint8,
      nonce,
      naclUtil.decodeBase64(groupKey),
    );

    return JSON.stringify({
      nonce: naclUtil.encodeBase64(nonce),
      encryptedData: naclUtil.encodeBase64(encryptedMessage),
    });
  }

  /**
   * Decrypt a message with a group key
   */
  decryptMessage(encryptedMessage: string, groupKey: string): string {
    const { nonce, encryptedData } = JSON.parse(encryptedMessage);

    const decrypted = nacl.secretbox.open(
      naclUtil.decodeBase64(encryptedData),
      naclUtil.decodeBase64(nonce),
      naclUtil.decodeBase64(groupKey),
    );

    if (!decrypted) {
      throw new Error('Failed to decrypt message');
    }

    return naclUtil.encodeUTF8(decrypted);
  }

  /**
   * Sign a message with a user's signing secret key
   */
  signMessage(message: string, signingSecretKey: string): string {
    const messageUint8 = naclUtil.decodeUTF8(message);
    const signature = nacl.sign.detached(
      messageUint8,
      naclUtil.decodeBase64(signingSecretKey),
    );

    return naclUtil.encodeBase64(signature);
  }

  /**
   * Verify a message signature
   */
  verifySignature(
    message: string,
    signature: string,
    signingPublicKey: string,
  ): boolean {
    return nacl.sign.detached.verify(
      naclUtil.decodeUTF8(message),
      naclUtil.decodeBase64(signature),
      naclUtil.decodeBase64(signingPublicKey),
    );
  }

  /**
   * Implements Double Ratchet Algorithm component - key derivation function
   * This is a simplified version, actual Signal Protocol implementation is more complex
   */
  deriveNextKey(currentKey: string, salt: string): string {
    const hmac = crypto.createHmac('sha256', salt);
    hmac.update(naclUtil.decodeBase64(currentKey));
    return naclUtil.encodeBase64(hmac.digest());
  }

  /**
   * Generate a one-time prekey for initial key exchange
   */
  generateOneTimePrekey(): { id: number; key: string } {
    const id = Math.floor(Math.random() * 100000);
    const key = naclUtil.encodeBase64(nacl.box.keyPair().publicKey);
    return { id, key };
  }

  /**
   * Generate multiple one-time prekeys for a user
   */
  generatePrekeys(count: number = 100): Array<{ id: number; key: string }> {
    const prekeys: Array<{ id: number; key: string }> = [];
    for (let i = 0; i < count; i++) {
      prekeys.push(this.generateOneTimePrekey());
    }
    return prekeys;
  }
}
