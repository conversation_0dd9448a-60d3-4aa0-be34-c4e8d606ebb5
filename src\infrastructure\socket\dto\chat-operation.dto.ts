import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  IsBoolean,
  IsOptional,
  IsArray,
  IsNotEmpty,
  IsObject,
} from 'class-validator';
import { SendPrivateMessageDto } from 'src/modules/messages/dto/send-private-message.dto';

export enum ChatType {
  GROUP = 'group',
  PRIVATE = 'private',
}

export class ChatOperationDto {
  @IsEnum(ChatType)
  chatType: ChatType;

  @IsString()
  chatId: string; // For group: groupId, For private: "member1Id_member2Id"
}
export type SocketSendMessageDto = SendMessageDto | SendPrivateMessageDto;

export class SendMessageDto extends ChatOperationDto {
  @IsNumber()
  senderId: number;

  @IsString()
  @IsNotEmpty()
  content: string;

  @IsString()
  @IsNotEmpty()
  nonce: string;

  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @IsNumber()
  @IsOptional()
  replyToMessageId?: number;

  @IsNumber()
  @IsOptional()
  fileId?: number;

  @IsOptional()
  sentAt?: Date;
}

export class UserTypingDto extends ChatOperationDto {
  @IsBoolean()
  isTyping: boolean;

  @IsOptional()
  @IsNumber()
  duration?: number; // typing duration in milliseconds
}

export class MessageReadDto extends ChatOperationDto {
  @IsArray()
  @IsNumber({}, { each: true })
  messageIds: number[];

  @IsOptional()
  @IsNumber()
  readerId?: number; // Will be set from client data if not provided
}

export class DeleteMessageDto extends ChatOperationDto {
  @IsNumber()
  messageId: number;

  @IsOptional()
  @IsNumber()
  deleterId?: number; // Will be set from client data if not provided
}

export class DeleteMessagesForEveryoneDto extends ChatOperationDto {
  @IsArray()
  @IsNumber({}, { each: true })
  messageIds: number[];

  @IsOptional()
  @IsNumber()
  deleterId?: number; // Will be set from client data if not provided
}

export function normalizeChatId(
  chatType: ChatType,
  chatId: string,
): {
  normalizedId: string;
  identifiers: number[];
} {
  if (chatType === ChatType.GROUP) {
    const groupId = parseInt(chatId);
    if (isNaN(groupId)) {
      throw new Error('Invalid group ID');
    }
    return {
      normalizedId: chatId,
      identifiers: [groupId],
    };
  } else if (chatType === ChatType.PRIVATE) {
    const parts = chatId.split('_').map(Number);
    if (parts.length !== 2 || parts.some(isNaN)) {
      throw new Error(
        'Invalid private chat ID format. Expected: "memberId1_memberId2"',
      );
    }
    // Sort to ensure consistent room naming
    const sortedIds = parts.sort((a, b) => a - b);
    return {
      normalizedId: sortedIds.join('_'),
      identifiers: sortedIds,
    };
  }

  throw new Error(`Unsupported chat type: ${chatType}`);
}
