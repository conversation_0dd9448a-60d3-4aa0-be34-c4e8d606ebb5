import {
  <PERSON>,
  Get,
  Param,
  Query,
  Res,
  ParseIntPipe,
} from '@nestjs/common';
import { Response } from 'express';
import { MediaDownloadService } from '../services/media-download.service';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
  ApiResponse,
} from '@nestjs/swagger';

@ApiTags('Media Download')
@ApiBearerAuth()
@Controller('media/download')
export class MediaDownloadController {
  constructor(private readonly mediaDownloadService: MediaDownloadService) {}

  @Get(':fileId')
  @ApiOperation({ summary: 'Download media file' })
  @ApiParam({ name: 'fileId', description: 'Media file ID' })
  @ApiQuery({ name: 'userId', type: Number })
  @ApiResponse({ status: 200, description: 'File download stream' })
  async downloadMediaFile(
    @Param('fileId') fileId: string,
    @Query('userId', ParseIntPipe) userId: number,
    @Res() res: Response,
  ) {
    const fileStream = await this.mediaDownloadService.downloadFile(
      fileId,
      userId,
    );
    return fileStream.pipe(res);
  }

  @Get(':fileId/thumbnail')
  @ApiOperation({ summary: 'Download thumbnail' })
  @ApiParam({ name: 'fileId', description: 'Media file ID' })
  @ApiQuery({ name: 'size', required: false, type: String })
  async downloadThumbnail(
    @Param('fileId') fileId: string,
    @Query('size') size: string = 'medium',
    @Res() res: Response,
  ) {
    const thumbnailStream = await this.mediaDownloadService.downloadThumbnail(
      fileId,
      size,
    );
    return thumbnailStream.pipe(res);
  }

  @Get(':fileId/compressed')
  @ApiOperation({ summary: 'Download compressed media file' })
  @ApiParam({ name: 'fileId', description: 'Media file ID' })
  @ApiQuery({ name: 'quality', required: false, type: String })
  async downloadCompressed(
    @Param('fileId') fileId: string,
    @Query('quality') quality: string = 'medium',
    @Res() res: Response,
  ) {
    const compressedStream = await this.mediaDownloadService.downloadCompressed(
      fileId,
      quality,
    );
    return compressedStream.pipe(res);
  }

  @Get(':fileId/url')
  @ApiOperation({ summary: 'Generate secure download URL' })
  @ApiParam({ name: 'fileId', description: 'Media file ID' })
  @ApiQuery({ name: 'userId', type: Number })
  @ApiQuery({ name: 'expiresIn', required: false, type: Number })
  async generateDownloadUrl(
    @Param('fileId') fileId: string,
    @Query('userId', ParseIntPipe) userId: number,
    @Query('expiresIn', ParseIntPipe) expiresIn: number = 3600,
  ) {
    const url = await this.mediaDownloadService.generateSecureUrl(
      fileId,
      userId,
      expiresIn,
    );
    return { downloadUrl: url, expiresIn };
  }

  @Get(':fileId/range')
  @ApiOperation({ summary: 'Download media file with range support' })
  @ApiParam({ name: 'fileId', description: 'Media file ID' })
  @ApiQuery({ name: 'start', type: Number })
  @ApiQuery({ name: 'end', type: Number })
  async downloadWithRange(
    @Param('fileId') fileId: string,
    @Query('start', ParseIntPipe) start: number,
    @Query('end', ParseIntPipe) end: number,
    @Res() res: Response,
  ) {
    const rangeStream = await this.mediaDownloadService.downloadRange(
      fileId,
      start,
      end,
    );
    return rangeStream.pipe(res);
  }
}
