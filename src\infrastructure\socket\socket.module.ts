import { Modu<PERSON> } from '@nestjs/common';
import { SocketGateway } from './socket.gateway';
import { WsAuthInterceptor } from './interceptors/ws-auth.interceptor';
import { JwtService } from '@nestjs/jwt';
import { UserPresenceHandler } from './handlers/user-presence.handler';
import { SystemHandler } from './handlers/system.handler';
import { RedisModule } from '../redis/redis.module';
import { RoomManagerService } from './services/room-manager.service';
import { SocketRoomService } from './services/socket-room.service';
import { ChatOperationHandler } from './handlers/chat-operation.handler';
import { RoomValidationService } from './services/room-validation.service';

@Module({
  imports: [RedisModule],
  providers: [
    SocketGateway,
    WsAuthInterceptor,
    JwtService,
    UserPresenceHandler,
    SystemHandler,
    RoomManagerService,
    {
      provide: 'ISocketRoomService',
      useClass: SocketRoomService,
    },
    RoomValidationService,
    {
      provide: 'IChatRoomValidator',
      useClass: RoomValidationService,
    },
    ChatOperationHandler,
  ],
  exports: [
    SocketGateway,
    { provide: 'ISocketRoomService', useClass: SocketRoomService },
    { provide: 'IChatRoomValidator', useClass: RoomValidationService },
  ],
})
export class SocketModule {}
