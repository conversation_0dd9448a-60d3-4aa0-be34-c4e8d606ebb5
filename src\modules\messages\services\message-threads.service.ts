import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, QueryRunner, Repository } from 'typeorm';
import { MessageThread } from '../entities/message-thread.entity';
import {
  CreateMessageThreadDto,
  UpdateMessageThreadDto,
} from '../dto/message-thread.dto';
import { SyncStateService } from './sync-state.service';
import { ChatType } from 'src/infrastructure/socket/dto/chat-operation.dto';
import { SyncState } from '../entities/sync-state.entity';

@Injectable()
export class MessageThreadService {
  constructor(
    @InjectRepository(MessageThread)
    private readonly messageThreadRepo: Repository<MessageThread>,
    private readonly syncStateService: SyncStateService,
  ) {}

  async findOrCreateThreadWithTransaction(
    memberId: number,
    chatType: ChatType,
    targetId: number,
    queryRunner: QueryRunner,
    welcomeMessage?: string,
    lastMessageId?: number,
  ): Promise<any> {
    let thread = await queryRunner.manager.findOne(MessageThread, {
      where: { memberId, chatType, targetId },
      relations: ['syncState'],
    });

    if (!thread) {
      thread = queryRunner.manager.create(MessageThread, {
        memberId,
        chatType,
        targetId,
        unreadCount: 0,
        isMuted: false,
        isPinned: false,
        welcomeMessage,
        lastMessageId,
        lastMessageAt: new Date(),
      });

      thread = await queryRunner.manager.save(MessageThread, thread);

      // Create sync state within transaction
      await this.syncStateService.createSyncStateWithTransaction(
        thread.id,
        queryRunner,
      );
    }

    return thread;
  }

  async findOrCreateThread(
    memberId: number,
    chatType: ChatType,
    targetId: number,
  ): Promise<MessageThread> {
    let thread = await this.messageThreadRepo.findOne({
      where: { memberId, chatType, targetId },
      relations: ['syncState'],
    });

    if (!thread) {
      thread = this.messageThreadRepo.create({
        memberId,
        chatType,
        targetId,
        unreadCount: 0,
        isMuted: false,
        isPinned: false,
      });
      thread = await this.messageThreadRepo.save(thread);
      await this.syncStateService.createSyncState(thread.id);
    }

    return thread;
  }

  async updateThreadAfterMessage(
    memberId: number,
    chatType: ChatType,
    targetId: number,
    messageId: number,
    messageSeq: number,
    isFromSelf: boolean = false,
  ): Promise<MessageThread> {
    const thread = await this.findOrCreateThread(memberId, chatType, targetId);

    // Update last message info
    thread.lastMessageId = messageId;

    // Only increment unread count if message is not from the member themselves
    if (!isFromSelf) {
      thread.unreadCount += 1;
    }

    // Mark sync state as having pending messages if not from self
    if (thread.syncState) {
      thread.syncState.hasPending = !isFromSelf;
      // Update lastSyncedSeq to current message sequence
      thread.syncState.lastSyncedSeq = messageSeq;
    }

    return await this.messageThreadRepo.save(thread);
  }

  async updateUnreadCount(
    memberId: number,
    chatType: ChatType,
    targetId: number,
    increment: number,
  ): Promise<void> {
    await this.messageThreadRepo
      .createQueryBuilder()
      .update(MessageThread)
      .set({
        unreadCount: () => `unread_count + ${increment}`,
        updatedAt: new Date(),
      })
      .where(
        'member_id = :memberId AND chat_type = :chatType AND target_id = :targetId',
        {
          memberId,
          chatType,
          targetId,
        },
      )
      .execute();
  }

  async markAsRead(
    memberId: number,
    chatType: ChatType,
    targetId: number,
    lastReadMessageSeq?: number,
  ): Promise<void> {
    const thread = await this.findOrCreateThread(memberId, chatType, targetId);

    thread.unreadCount = 0;

    if (thread.syncState && lastReadMessageSeq) {
      thread.syncState.lastAckedSeq = lastReadMessageSeq;
      thread.syncState.hasPending = false;
    }

    await this.messageThreadRepo.save(thread);
  }

  async getMemberThreads(
    memberId: number,
    chatType?: ChatType,
  ): Promise<MessageThread[]> {
    const queryBuilder = this.messageThreadRepo
      .createQueryBuilder('thread')
      .leftJoinAndSelect('thread.syncState', 'syncState')
      .where('thread.member_id = :memberId', { memberId })
      .orderBy('thread.updated_at', 'DESC');

    if (chatType) {
      queryBuilder.andWhere('thread.chat_type = :chatType', { chatType });
    }

    return await queryBuilder.getMany();
  }

  async updateThreadSettings(
    memberId: number,
    chatType: ChatType,
    targetId: number,
    settings: UpdateMessageThreadDto,
  ): Promise<MessageThread> {
    const thread = await this.findOrCreateThread(memberId, chatType, targetId);

    if (settings.isMuted !== undefined) {
      thread.isMuted = settings.isMuted;
    }

    if (settings.isPinned !== undefined) {
      thread.isPinned = settings.isPinned;
    }

    return await this.messageThreadRepo.save(thread);
  }

  async deleteThread(
    memberId: number,
    chatType: ChatType,
    targetId: number,
  ): Promise<void> {
    await this.messageThreadRepo.delete({
      memberId,
      chatType,
      targetId,
    });
  }

  async getThreadById(id: number): Promise<MessageThread> {
    const thread = await this.messageThreadRepo.findOne({
      where: { id },
      relations: ['syncState'],
    });

    if (!thread) {
      throw new NotFoundException(`Thread with id ${id} not found`);
    }

    return thread;
  }

  async getThreadByTargetId(
    memberId: number,
    chatType: ChatType,
    targetId: number,
  ): Promise<MessageThread | null> {
    return this.messageThreadRepo.findOne({
      where: { memberId, chatType, targetId },
      relations: ['syncState'],
    });
  }

  async bulkUpdateThreadsForMessage(
    memberIds: number[],
    chatType: ChatType,
    targetId: number,
    messageId: number,
    messageSeq: number,
    senderId: number,
  ): Promise<void> {
    if (memberIds.length === 0) return;

    const existingThreads = await this.messageThreadRepo.find({
      where: {
        memberId: In(memberIds),
        chatType,
        targetId,
      },
      relations: ['syncState'],
    });

    const existingMemberIds = existingThreads.map((t) => t.memberId);
    const missingMemberIds = memberIds.filter(
      (id) => !existingMemberIds.includes(id),
    );

    for (const memberId of missingMemberIds) {
      await this.findOrCreateThread(memberId, chatType, targetId);
    }

    // update threads
    await this.messageThreadRepo
      .createQueryBuilder()
      .update(MessageThread)
      .set({
        lastMessageId: messageId,
        unreadCount: () =>
          `CASE WHEN member_id = ${senderId} THEN unread_count ELSE unread_count + 1 END`,
        updatedAt: new Date(),
      })
      .where(
        'chat_type = :chatType AND target_id = :targetId AND member_id IN (:...memberIds)',
        { chatType, targetId, memberIds },
      )
      .execute();

    // update sync states via service
    for (const thread of existingThreads) {
      await this.syncStateService.updateSyncState(thread.id, {
        lastSyncedSeq: messageSeq,
        hasPending: thread.memberId === senderId ? false : true,
      });
    }
  }
}
