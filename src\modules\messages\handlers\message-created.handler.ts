// // Message Created Handler
// import { Injectable, Logger } from '@nestjs/common';
// import { OnEvent, EventEmitter2 } from '@nestjs/event-emitter';
// import { MessagesService } from '../services/messages.service';
// import { EVENT_NAMES } from '../../../common/constants/event-names';
// import {
//   SendMessageRequestedEvent,
//   MessageReadRequestedEvent,
//   DeleteMessageForMeRequestedEvent,
//   DeleteMessagesForEveryoneRequestedEvent,
//   MessageSentEvent,
//   MessageReadEvent,
//   MessageDeletedEvent,
// } from '../../../common/events/chat.events';

// @Injectable()
// export class MessageOperationHandler {
//   private readonly logger = new Logger(MessageOperationHandler.name);

//   constructor(
//     private readonly messagesService: MessagesService,
//     private readonly eventEmitter: EventEmitter2,
//   ) {}

//   @OnEvent(EVENT_NAMES.SEND_MESSAGE_REQUESTED)
//   async onSendMessageRequested(event: SendMessageRequestedEvent) {
//     try {
//       const { payload } = event;

//       // Process and save message
//       const savedMessage = await this.messagesService.createMessage({
//         chatType: payload.chatType,
//         chatId: payload.chatId,
//         senderId: payload.senderId,
//         content: payload.content,
//         messageType: payload.messageType,
//         attachments: payload.attachments,
//         replyToMessageId: payload.replyToMessageId,
//         metadata: payload.metadata,
//       });

//       // Emit back to socket gateway for broadcasting
//       this.eventEmitter.emit(EVENT_NAMES.MESSAGE_SENT, new MessageSentEvent({
//         chatType: payload.chatType,
//         chatId: payload.chatId,
//         message: savedMessage,
//         excludeMemberId: payload.senderId, // Don't send back to sender
//       }));

//     } catch (error) {
//       this.logger.error('Error processing send message request:', error);
//       // Could emit error event here
//     }
//   }

//   @OnEvent(EVENT_NAMES.MESSAGE_READ_REQUESTED)
//   async onMessageReadRequested(event: MessageReadRequestedEvent) {
//     try {
//       const { payload } = event;

//       // Mark messages as read
//       const readRecords = await this.messagesService.markMessagesAsRead(
//         payload.chatType,
//         payload.chatId,
//         payload.readerId,
//         payload.messageIds,
//         payload.readAt,
//       );

//       if (readRecords.length > 0) {
//         // Emit back to socket gateway for broadcasting
//         this.eventEmitter.emit(EVENT_NAMES.MESSAGE_READ, new MessageReadEvent({
//           chatType: payload.chatType,
//           chatId: payload.chatId,
//           readInfo: {
//             readerId: payload.readerId,
//             messageIds: readRecords.map(r => r.messageId),
//             readAt: payload.readAt,
//           },
//         }));
//       }

//     } catch (error) {
//       this.logger.error('Error processing message read request:', error);
//     }
//   }

//   @OnEvent(EVENT_NAMES.DELETE_MESSAGE_FOR_ME_REQUESTED)
//   async onDeleteMessageForMeRequested(event: DeleteMessageForMeRequestedEvent) {
//     try {
//       const { payload } = event;

//       // Soft delete for the specific user
//       await this.messagesService.deleteMessageForMember(
//         payload.messageId,
//         payload.deleterId,
//         payload.deletedAt,
//       );

//       // No need to broadcast this - it only affects the requesting user

//     } catch (error) {
//       this.logger.error('Error processing delete message for me request:', error);
//     }
//   }

//   @OnEvent(EVENT_NAMES.DELETE_MESSAGES_FOR_EVERYONE_REQUESTED)
//   async onDeleteMessagesForEveryoneRequested(event: DeleteMessagesForEveryoneRequestedEvent) {
//     try {
//       const { payload } = event;

//       // Validate permission
//       const canDelete = await this.messagesService.canDeleteMessagesForEveryone(
//         payload.deleterId,
//         payload.messageIds,
//         payload.chatType,
//         payload.chatId,
//       );

//       if (!canDelete) {
//         this.logger.warn(`User ${payload.deleterId} attempted to delete messages without permission`);
//         return;
//       }

//       // Delete for everyone
//       const deletedMessageIds = await this.messagesService.deleteMessagesForEveryone(
//         payload.messageIds,
//         payload.deleterId,
//         payload.deletedAt,
//       );

//       if (deletedMessageIds.length > 0) {
//         // Emit back to socket gateway for broadcasting
//         this.eventEmitter.emit(EVENT_NAMES.MESSAGE_DELETED, new MessageDeletedEvent({
//           chatType: payload.chatType,
//           chatId: payload.chatId,
//           deletionInfo: {
//             deleterId: payload.deleterId,
//             messageIds: deletedMessageIds,
//             deletedAt: payload.deletedAt,
//             deletionType: 'for_everyone',
//           },
//         }));
//       }

//     } catch (error) {
//       this.logger.error('Error processing delete messages for everyone request:', error);
//     }
//   }
// }
