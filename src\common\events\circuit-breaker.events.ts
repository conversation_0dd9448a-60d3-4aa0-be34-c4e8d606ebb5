import { BaseEvent } from './base.event';
import { EVENT_NAMES } from '../constants/event-names';

export class CircuitBreakerOpenedEvent extends BaseEvent {
  readonly name = EVENT_NAMES.CIRCUIT_BREAKER_OPENED;
  constructor(public readonly payload: { service: string; reason?: string }) {
    super();
  }
}

export class CircuitBreakerClosedEvent extends BaseEvent {
  readonly name = EVENT_NAMES.CIRCUIT_BREAKER_CLOSED;
  constructor(public readonly payload: { service: string }) {
    super();
  }
}

export class CircuitBreakerHalfOpenEvent extends BaseEvent {
  readonly name = EVENT_NAMES.CIRCUIT_BREAKER_HALF_OPEN;
  constructor(public readonly payload: { service: string }) {
    super();
  }
}
