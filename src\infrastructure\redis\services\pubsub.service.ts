import { Injectable, Inject, Logger, OnM<PERSON><PERSON><PERSON><PERSON>roy } from '@nestjs/common';
import { <PERSON><PERSON> } from '@nestjs/schedule';
import {
  REDIS_CLIENT,
  REDIS_PUBLISHER,
  REDIS_SUBSCRIBER,
} from '../constants/redis.constants';
import {
  ONLINE_PRESENCE_KEY,
  ONLINE_MEMBERS_HASH,
} from '../constants/redis.constants';
import {
  MemberStatus,
  DeviceInfo,
  PresenceStatus,
} from '../interfaces/redis.types';
import { RedisClient } from '../interfaces/redis-client.interface';
import { PresenceUpdatedEvent } from 'src/common/events/presence.events';
import { EventBusService } from 'src/infrastructure/events/event-bus.service';

@Injectable()
export class PubSubService implements OnModuleDestroy {
  private readonly logger = new Logger(PubSubService.name);
  private readonly presenceChannelName = 'presence-channel';

  constructor(
    @Inject(REDIS_CLIENT) private readonly redisClient: RedisClient,
    @Inject(REDIS_PUBLISHER) private readonly publisher: RedisClient,
    @Inject(REDIS_SUBSCRIBER) private readonly subscriber: RedisClient,
    private readonly eventBus: EventBusService,
  ) {
    this.setupSubscriber();
  }

  /**
   * Set up Redis subscriber for presence channel
   */
  private setupSubscriber(): void {
    this.subscriber.subscribe(this.presenceChannelName, (err) => {
      if (err) {
        this.logger.error(
          `Failed to subscribe to presence channel: ${err.message}`,
        );
        return;
      }
      this.logger.log(`Subscribed to ${this.presenceChannelName}`);
    });

    this.subscriber.on('message', (channel, message) => {
      if (channel === this.presenceChannelName) {
        try {
          const data = JSON.parse(message);
          this.logger.debug(
            `Received presence update: ${JSON.stringify(data)}`,
          );
          // Handle presence event (can be extended based on your needs)
        } catch (e) {
          this.logger.error(`Error processing presence message: ${e.message}`);
        }
      }
    });
  }

  /**
   * Online Presence Management
   */

  /**
   * Set member online on a specific device
   */
  async setMemberOnline(memberId: number, deviceId: string): Promise<void> {
    const deviceKey = `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`;
    const memberDevicesKey = `member_devices:${memberId}`;
    const now = Date.now().toString();

    try {
      // Set device-specific presence with TTL (45 seconds)
      await this.redisClient.set(deviceKey, now, 'EX', 45);

      // Track device under member's device set, set expire to 90 seconds
      await this.redisClient.sadd(memberDevicesKey, deviceId);
      await this.redisClient.expire(memberDevicesKey, 90);

      // Update overall member status
      await this.updateMemberOverallStatus(memberId);

      this.logger.debug(`Member ${memberId} is online on device ${deviceId}`);
    } catch (error) {
      this.logger.error(`Error setting member ${memberId} online:`, error);
      throw error;
    }
  }

  /**
   * Refresh presence for a member on a specific device
   */
  async refreshPresence(memberId: number, deviceId: string): Promise<void> {
    const deviceKey = `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`;
    const memberDevicesKey = `member_devices:${memberId}`;
    const now = Date.now().toString();

    try {
      const exists = await this.redisClient.exists(deviceKey);
      if (!exists) {
        // If device presence expired, set member online again
        await this.setMemberOnline(memberId, deviceId);
        return;
      }

      // Refresh TTL and timestamp for device presence and member's devices set
      await this.redisClient.set(deviceKey, now, 'EX', 120);
      await this.redisClient.expire(memberDevicesKey, 300);

      // Update overall status
      await this.updateMemberOverallStatus(memberId);
    } catch (error) {
      this.logger.error(
        `Error refreshing presence for member ${memberId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Set member offline on a specific device
   */
  async setMemberOffline(memberId: number, deviceId: string): Promise<void> {
    const deviceKey = `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`;
    const memberDevicesKey = `member_devices:${memberId}`;

    try {
      // Remove device presence and device from member set
      await this.redisClient.del(deviceKey);
      await this.redisClient.srem(memberDevicesKey, deviceId);

      // Update overall status after device removal
      await this.updateMemberOverallStatus(memberId);

      this.logger.debug(`Member ${memberId} is offline on device ${deviceId}`);
    } catch (error) {
      this.logger.error(`Error setting member ${memberId} offline:`, error);
      throw error;
    }
  }

  /**
   * Update member's overall online status based on all devices
   */
  private async updateMemberOverallStatus(memberId: number): Promise<void> {
    try {
      const memberDevicesKey = `member_devices:${memberId}`;
      const devices = await this.redisClient.smembers(memberDevicesKey);

      let overallStatus: PresenceStatus = 'offline';
      let latestActivity: number | null = null;
      let hasActiveDevices = false;
      let activePresences: number[] = [];
      let storedLastSeen: string | null = null;

      if (devices.length > 0) {
        const presenceChecks = devices.map((deviceId) =>
          this.redisClient.get(
            `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`,
          ),
        );

        const presences = await Promise.all(presenceChecks);

        activePresences = presences
          .filter((p) => p !== null)
          .map((p) => parseInt(p as string, 10))
          .filter((n) => !isNaN(n));

        if (activePresences.length > 0) {
          hasActiveDevices = true;
          overallStatus = 'online';
          latestActivity = Math.max(...activePresences);

          const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
          if (latestActivity < fiveMinutesAgo) {
            overallStatus = 'away';
          }
        }
      }

      // Update Redis hash with latest timestamp or remove if offline
      if (hasActiveDevices && latestActivity) {
        await this.redisClient.hset(
          ONLINE_MEMBERS_HASH,
          memberId.toString(),
          latestActivity.toString(),
        );
      } else {
        // Try to fetch existing lastSeen before deleting the hash entry
        storedLastSeen = await this.redisClient.hget(
          ONLINE_MEMBERS_HASH,
          memberId.toString(),
        );

        await this.redisClient.hdel(ONLINE_MEMBERS_HASH, memberId.toString());
      }

      const statusMessage = {
        memberId,
        status: overallStatus,
        lastSeen: hasActiveDevices
          ? latestActivity
          : storedLastSeen
            ? parseInt(storedLastSeen, 10)
            : null,
        timestamp: Date.now().toString(),
        devices: devices.length,
        activeDevices: hasActiveDevices ? activePresences.length : 0,
      };

      this.eventBus.publish(
        new PresenceUpdatedEvent({
          memberId: statusMessage.memberId,
          status: statusMessage.status,
          lastSeen: hasActiveDevices
            ? latestActivity
            : storedLastSeen
              ? parseInt(storedLastSeen, 10)
              : null,
          timestamp: Date.now().toString(),
        }),
      );

      await this.publisher.publish(
        this.presenceChannelName,
        JSON.stringify(statusMessage),
      );
    } catch (error) {
      this.logger.error(
        `Error updating overall status for member ${memberId}:`,
        error,
      );
    }
  }

  /**
   * Get member status with presence information
   */
  async getMemberStatus(memberId: number): Promise<MemberStatus> {
    try {
      const memberDevicesKey = `member_devices:${memberId}`;
      const devices = await this.redisClient.smembers(memberDevicesKey);

      if (devices.length === 0) {
        return {
          memberId,
          status: 'offline',
          devices: [],
          activeDevices: 0,
        };
      }

      const presenceChecks = devices.map((deviceId) =>
        this.redisClient.get(`${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`),
      );
      const presences = await Promise.all(presenceChecks);

      const activePresences = presences
        .filter((p) => p !== null)
        .map((p) => parseInt(p as string, 10))
        .filter((n) => !isNaN(n));

      if (activePresences.length === 0) {
        const lastSeenStr = await this.redisClient.hget(
          ONLINE_MEMBERS_HASH,
          memberId.toString(),
        );
        const lastSeen = lastSeenStr ? parseInt(lastSeenStr, 10) : undefined;

        return {
          memberId,
          status: 'offline',
          devices,
          activeDevices: 0,
          lastSeen,
        };
      }

      const latestActivity = Math.max(...activePresences);
      const fiveMinutesAgo = Date.now() - 5 * 60 * 1000;
      const status = latestActivity < fiveMinutesAgo ? 'away' : 'online';

      return {
        memberId,
        status,
        lastSeen: latestActivity,
        devices,
        activeDevices: activePresences.length,
      };
    } catch (error) {
      this.logger.error(`Error getting status for member ${memberId}:`, error);
      return {
        memberId,
        status: 'offline',
        devices: [],
        activeDevices: 0,
      };
    }
  }

  /**
   * Get multiple member statuses in batch
   */
  async getMultipleMemberStatuses(
    memberIds: number[],
  ): Promise<MemberStatus[]> {
    try {
      const statusPromises = memberIds.map((id) => this.getMemberStatus(id));
      return await Promise.all(statusPromises);
    } catch (error) {
      this.logger.error('Error getting multiple member statuses:', error);
      return memberIds.map((memberId) => ({
        memberId,
        status: 'offline',
        devices: [],
        activeDevices: 0,
      }));
    }
  }

  /**
   * Get all online members
   */
  async getOnlineMembers(): Promise<number[]> {
    try {
      const entries = await this.redisClient.hgetall(ONLINE_MEMBERS_HASH);
      return Object.keys(entries).map((id) => parseInt(id, 10));
    } catch (error) {
      this.logger.error('Error getting online members:', error);
      return [];
    }
  }

  /**
   * Check if member is online
   */
  async isMemberOnline(memberId: number): Promise<boolean> {
    try {
      const exists = await this.redisClient.hexists(
        ONLINE_MEMBERS_HASH,
        memberId.toString(),
      );
      if (!exists) return false;

      const status = await this.getMemberStatus(memberId);
      return status.status === 'online' || status.status === 'away';
    } catch (error) {
      this.logger.error(
        `Error checking if member ${memberId} is online:`,
        error,
      );
      return false;
    }
  }

  /**
   * Get member device information
   */
  async getMemberDevices(memberId: number): Promise<DeviceInfo> {
    try {
      const memberDevicesKey = `member_devices:${memberId}`;
      const devices = await this.redisClient.smembers(memberDevicesKey);

      if (devices.length === 0) {
        return { devices: [], activeDevices: [] };
      }

      const presenceChecks = devices.map(async (deviceId) => {
        const presence = await this.redisClient.get(
          `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`,
        );
        return { deviceId, active: presence !== null };
      });

      const deviceStatuses = await Promise.all(presenceChecks);
      const activeDevices = deviceStatuses
        .filter((d) => d.active)
        .map((d) => d.deviceId);

      return { devices, activeDevices };
    } catch (error) {
      this.logger.error(`Error getting devices for member ${memberId}:`, error);
      return { devices: [], activeDevices: [] };
    }
  }

  /**
   * Subscribe to presence changes
   */
  subscribeToPresenceChanges(callback: (message: string) => void): void {
    this.subscriber.on('message', (channel, message) => {
      if (channel === this.presenceChannelName) {
        try {
          callback(message);
        } catch (e) {
          this.logger.error(
            `Error in presence callback: ${(e as Error).message}`,
          );
        }
      }
    });
  }

  /**
   * Cleanup expired presences (runs every minute)
   */
  @Cron('*/1 * * * *')
  protected async cleanupExpiredPresences(): Promise<void> {
    try {
      // Get all keys tracking member devices
      const memberDeviceKeys = await this.redisClient.keys('member_devices:*');

      for (const memberDeviceKey of memberDeviceKeys) {
        const memberId = parseInt(memberDeviceKey.split(':')[1], 10);
        if (isNaN(memberId)) continue;

        const devices = await this.redisClient.smembers(memberDeviceKey);

        for (const deviceId of devices) {
          const presenceKey = `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`;
          const exists = await this.redisClient.exists(presenceKey);

          if (!exists) {
            // Remove expired device from member's device set
            await this.redisClient.srem(memberDeviceKey, deviceId);
          }
        }

        // Update member overall status after cleanup
        await this.updateMemberOverallStatus(memberId);
      }

      this.logger.debug('Completed presence cleanup');
    } catch (error) {
      this.logger.error('Error during presence cleanup:', error);
    }
  }

  /**
   * Get debug information for member presence
   */
  async getPresenceDebugInfo(memberId: number): Promise<{
    memberDevices: string[];
    devicePresences: Array<{
      deviceId: string;
      timestamp: string | null;
      active: boolean;
    }>;
    overallStatus: MemberStatus;
    hashEntry: string | null;
  }> {
    try {
      const memberDevicesKey = `member_devices:${memberId}`;
      const memberDevices = await this.redisClient.smembers(memberDevicesKey);

      const devicePresences = await Promise.all(
        memberDevices.map(async (deviceId) => {
          const presenceKey = `${ONLINE_PRESENCE_KEY}:${memberId}:${deviceId}`;
          const timestamp = await this.redisClient.get(presenceKey);
          return {
            deviceId,
            timestamp,
            active: timestamp !== null,
          };
        }),
      );

      const overallStatus = await this.getMemberStatus(memberId);
      const hashEntry = await this.redisClient.hget(
        ONLINE_MEMBERS_HASH,
        memberId.toString(),
      );

      return {
        memberDevices,
        devicePresences,
        overallStatus,
        hashEntry,
      };
    } catch (error) {
      this.logger.error(
        `Error getting debug info for member ${memberId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Publish a message to a channel
   */
  async publish(channel: string, message: string): Promise<void> {
    try {
      await this.publisher.publish(channel, message);
    } catch (error) {
      this.logger.error(`Error publishing to channel ${channel}:`, error);
      throw error;
    }
  }

  /**
   * Subscribe to a channel
   */
  async subscribe(channel: string): Promise<void> {
    try {
      await this.subscriber.subscribe(channel);
    } catch (error) {
      this.logger.error(`Error subscribing to channel ${channel}:`, error);
      throw error;
    }
  }

  /**
   * Unsubscribe from a channel
   */
  async unsubscribe(channel: string): Promise<void> {
    try {
      await this.subscriber.unsubscribe(channel);
    } catch (error) {
      this.logger.error(`Error unsubscribing from channel ${channel}:`, error);
      throw error;
    }
  }

  /**
   * Cleanup when the module is destroyed
   */
  async onModuleDestroy(): Promise<void> {
    try {
      this.logger.warn('Closing PubSub Redis connections...');
      await this.publisher.quit();
      await this.subscriber.quit();
    } catch (error) {
      this.logger.error('Error closing PubSub Redis connections:', error);
    }
  }
}
