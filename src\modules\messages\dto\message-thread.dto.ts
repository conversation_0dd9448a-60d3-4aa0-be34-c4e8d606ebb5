import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsOptional,
  IsPositive,
  Min,
} from 'class-validator';

export enum ChatType {
  GROUP = 'group',
  PRIVATE = 'private',
}

export class CreateMessageThreadDto {
  @ApiProperty({
    example: 1,
    description: 'The ID of the user creating the thread',
  })
  @IsNumber()
  @IsPositive()
  userId: number;

  @ApiProperty({
    enum: ChatType,
    example: ChatType.GROUP,
    description: 'Type of chat (group or private)',
  })
  @IsEnum(ChatType)
  chatType: ChatType;

  @ApiProperty({ example: 2, description: 'The target user ID or group ID' })
  @IsNumber()
  @IsPositive()
  targetId: number;

  @ApiPropertyOptional({
    example: 101,
    description: 'Last message ID in the thread',
  })
  @IsNumber()
  @IsOptional()
  @IsPositive()
  lastMessageId?: number;

  @ApiPropertyOptional({ example: 0, description: 'Unread messages count' })
  @IsNumber()
  @IsOptional()
  @Min(0)
  unreadCount?: number = 0;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether the thread is muted',
  })
  @IsBoolean()
  @IsOptional()
  isMuted?: boolean = false;

  @ApiPropertyOptional({
    example: false,
    description: 'Whether the thread is pinned',
  })
  @IsBoolean()
  @IsOptional()
  isPinned?: boolean = false;
}

export class UpdateMessageThreadDto {
  @ApiPropertyOptional({
    example: 200,
    description: 'Last message ID to update',
  })
  @IsNumber()
  @IsOptional()
  @IsPositive()
  lastMessageId?: number;

  @ApiPropertyOptional({ example: 5, description: 'New unread count' })
  @IsNumber()
  @IsOptional()
  @Min(0)
  unreadCount?: number;

  @ApiPropertyOptional({ example: true, description: 'Mute/unmute thread' })
  @IsBoolean()
  @IsOptional()
  isMuted?: boolean;

  @ApiPropertyOptional({ example: true, description: 'Pin/unpin thread' })
  @IsBoolean()
  @IsOptional()
  isPinned?: boolean;
}

export class UpdateUnreadCountDto {
  @ApiProperty({ example: 1, description: 'User ID' })
  @IsNumber()
  @IsPositive()
  userId: number;

  @ApiProperty({ enum: ChatType, example: ChatType.PRIVATE })
  @IsEnum(ChatType)
  chatType: ChatType;

  @ApiProperty({ example: 2, description: 'Target ID (user or group)' })
  @IsNumber()
  @IsPositive()
  targetId: number;

  @ApiProperty({ example: 1, description: 'Increment (can be negative)' })
  @IsNumber()
  increment: number;
}

export class MarkAsReadDto {
  @ApiProperty({ example: 1 })
  @IsNumber()
  @IsPositive()
  userId: number;

  @ApiProperty({ enum: ChatType })
  @IsEnum(ChatType)
  chatType: ChatType;

  @ApiProperty({ example: 2 })
  @IsNumber()
  @IsPositive()
  targetId: number;

  @ApiPropertyOptional({
    example: 300,
    description: 'Last read message sequence',
  })
  @IsNumber()
  @IsOptional()
  @IsPositive()
  lastReadMessageSeq?: number;
}

export class GetUserThreadsDto {
  @ApiProperty({ example: 1 })
  @IsNumber()
  @IsPositive()
  userId: number;

  @ApiPropertyOptional({ enum: ChatType, example: ChatType.GROUP })
  @IsEnum(ChatType)
  @IsOptional()
  chatType?: ChatType;
}

export class ThreadIdentifierDto {
  @ApiProperty({ example: 1 })
  @IsNumber()
  @IsPositive()
  userId: number;

  @ApiProperty({ enum: ChatType })
  @IsEnum(ChatType)
  chatType: ChatType;

  @ApiProperty({ example: 2 })
  @IsNumber()
  @IsPositive()
  targetId: number;
}

export class BulkUpdateThreadsDto {
  @ApiProperty({ example: [1, 2, 3], description: 'IDs of members to update' })
  @IsNumber({}, { each: true })
  @IsPositive({ each: true })
  memberIds: number[];

  @ApiProperty({ enum: ChatType })
  @IsEnum(ChatType)
  chatType: ChatType;

  @ApiProperty({ example: 2, description: 'Target ID (user or group)' })
  @IsNumber()
  @IsPositive()
  targetId: number;

  @ApiProperty({
    example: 555,
    description: 'Message ID to update thread with',
  })
  @IsNumber()
  @IsPositive()
  messageId: number;

  @ApiProperty({ example: 42, description: 'Message sequence number' })
  @IsNumber()
  @IsPositive()
  messageSeq: number;

  @ApiProperty({ example: 10, description: 'Sender ID' })
  @IsNumber()
  @IsPositive()
  senderId: number;
}
