import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import * as admin from 'firebase-admin';
import * as path from 'path';
import * as fs from 'fs';
import { SilentNotificationDto } from '../dto/silent-notification.dto';
import { SendNotificationDto } from '../dto/send-notification.dto';
import { PRIORITY } from '../constants/notification.constants';

interface FCMResult {
  validTokens: string[];
  invalidTokens: string[];
  totalSent: number;
  totalFailed: number;
}

@Injectable()
export class FcmService implements OnModuleInit {
  private readonly logger = new Logger(FcmService.name);
  private readonly batchSize = 500; // FCM limit
  private readonly maxRetries = 2;
  private readonly retryDelay = 1000; // 1 second
  private readonly concurrencyLimit = 5; // Concurrent batches
  private readonly individualSendConcurrency = 10; // Individual sends

  onModuleInit() {
    if (!admin.apps.length) {
      const keyPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
      if (!keyPath || !fs.existsSync(keyPath)) {
        throw new Error(
          'Firebase service account key path is invalid or missing.',
        );
      }
      admin.initializeApp({
        credential: admin.credential.cert(require(path.resolve(keyPath))),
      });
      this.logger.log('Firebase Admin initialized');
    }
  }

  async sendSilentNotification(dto: SilentNotificationDto): Promise<FCMResult> {
    return this.sendNotification({
      tokens: dto.tokens,
      data: dto.data,
      priority: PRIORITY.HIGH,
    });
  }

  async sendNotification(dto: SendNotificationDto): Promise<FCMResult> {
    if (!dto.tokens?.length) {
      this.logger.warn('No tokens provided for notification');
      return this.emptyResult();
    }

    const uniqueTokens = [...new Set(dto.tokens)];
    const batches = this.createBatches(uniqueTokens);
    const results = this.emptyResult();

    await this.processBatches(batches, dto, results);

    this.logResults(results);
    return results;
  }

  private createBatches(tokens: string[]): string[][] {
    const batches: string[][] = [];
    for (let i = 0; i < tokens.length; i += this.batchSize) {
      batches.push(tokens.slice(i, i + this.batchSize));
    }
    return batches;
  }

  private async processBatches(
    batches: string[][],
    dto: SendNotificationDto,
    results: FCMResult,
  ) {
    for (let i = 0; i < batches.length; i += this.concurrencyLimit) {
      const batchGroup = batches.slice(i, i + this.concurrencyLimit);
      const batchResults = await Promise.allSettled(
        batchGroup.map((batch, idx) =>
          this.processBatchWithRetry(batch, dto, i + idx + 1, batches.length),
        ),
      );

      batchResults.forEach((result) => {
        if (result.status === 'fulfilled') {
          this.mergeResults(results, result.value);
        } else {
          const batchIndex = batchResults.indexOf(result);
          this.handleFailedBatch(
            batchGroup[batchIndex],
            results,
            result.reason,
          );
        }
      });
    }
  }

  private async processBatchWithRetry(
    batch: string[],
    dto: SendNotificationDto,
    batchNum: number,
    totalBatches: number,
    attempt = 1,
  ): Promise<FCMResult> {
    try {
      const messages = batch.map((token) => this.createMessage(token, dto));
      const response = await admin.messaging().sendEach(messages);

      this.logBatchResult(batchNum, totalBatches, response, batch.length);
      return this.processBatchResponse(batch, response);
    } catch (error) {
      if (attempt <= this.maxRetries) {
        await this.delay(this.retryDelay * attempt);
        return this.processBatchWithRetry(
          batch,
          dto,
          batchNum,
          totalBatches,
          attempt + 1,
        );
      }

      // Fallback to individual sends if batch completely fails
      this.logger.warn(
        `Batch ${batchNum} failed, falling back to individual sends`,
      );
      return this.processIndividualSends(batch, dto);
    }
  }

  private async processIndividualSends(
    tokens: string[],
    dto: SendNotificationDto,
  ): Promise<FCMResult> {
    const result = this.emptyResult();
    const activeSlots = new Array(this.individualSendConcurrency).fill(null);

    await Promise.all(
      tokens.map(async (token) => {
        const slotIndex = await this.acquireSlot(activeSlots);
        try {
          await this.sendWithRetry(this.createMessage(token, dto));
          result.validTokens.push(token);
          result.totalSent++;
        } catch (error) {
          result.invalidTokens.push(token);
          result.totalFailed++;
          this.logTokenError(token, error);
        } finally {
          activeSlots[slotIndex] = null;
        }
      }),
    );

    return result;
  }

  private async sendWithRetry(
    message: admin.messaging.Message,
    attempt = 1,
  ): Promise<void> {
    try {
      await admin.messaging().send(message);
    } catch (error) {
      if (attempt < this.maxRetries && this.isRetryableError(error)) {
        await this.delay(this.retryDelay * attempt);
        return this.sendWithRetry(message, attempt + 1);
      }
      throw error;
    }
  }

  private createMessage(
    token: string,
    dto: SendNotificationDto,
  ): admin.messaging.Message {
    const hasNotification = dto.title && dto.body;

    return {
      token,
      notification: hasNotification
        ? {
            title: dto.title!,
            body: dto.body!,
          }
        : undefined,
      data: dto.data,
      android: {
        priority: dto.priority || PRIORITY.HIGH,
        ...(dto.data?.collapse_key && { collapseKey: dto.data.collapse_key }),
      },
      apns: {
        headers: {
          'apns-priority': '10',
          ...(!hasNotification && { 'apns-push-type': 'background' }),
        },
        payload: {
          aps: hasNotification
            ? {
                alert: { title: dto.title!, body: dto.body! },
                sound: 'default',
              }
            : { 'content-available': 1 },
        },
      },
      webpush: {
        headers: {
          TTL: '86400', // 24 hours
        },
      },
    };
  }

  async validateTokens(tokens: string[]): Promise<{
    valid: string[];
    invalid: string[];
  }> {
    const result = { valid: [] as string[], invalid: [] as string[] };
    const messages = tokens.map((token) => ({
      token,
      data: { test: 'validation' },
    }));

    try {
      const response = await admin.messaging().sendEach(messages, true);
      response.responses.forEach((resp, index) => {
        if (resp.success) {
          result.valid.push(tokens[index]);
        } else {
          const errorCode = resp.error?.code;

          // Only treat token as invalid if app is uninstalled or token revoked
          if (
            errorCode === 'messaging/registration-token-not-registered' ||
            errorCode === 'messaging/invalid-argument'
          ) {
            result.invalid.push(tokens[index]);
          } else {
            // Treat other failures (offline, network issues) as valid
            result.valid.push(tokens[index]);
          }
        }
      });
    } catch (error) {
      this.logger.error('Token validation failed:', error);
    }

    return result;
  }

  // Helper Methods
  private emptyResult(): FCMResult {
    return { validTokens: [], invalidTokens: [], totalSent: 0, totalFailed: 0 };
  }

  private mergeResults(target: FCMResult, source: FCMResult) {
    target.validTokens.push(...source.validTokens);
    target.invalidTokens.push(...source.invalidTokens);
    target.totalSent += source.totalSent;
    target.totalFailed += source.totalFailed;
  }

  private processBatchResponse(
    tokens: string[],
    response: admin.messaging.BatchResponse,
  ): FCMResult {
    const result = this.emptyResult();
    response.responses.forEach((resp, index) => {
      if (resp.success) {
        result.validTokens.push(tokens[index]);
        result.totalSent++;
      } else {
        result.invalidTokens.push(tokens[index]);
        result.totalFailed++;
        this.logTokenError(tokens[index], resp.error);
      }
    });
    return result;
  }

  private async acquireSlot(slots: any[]): Promise<number> {
    return new Promise((resolve) => {
      const tryAcquire = () => {
        const index = slots.findIndex((slot) => slot === null);
        if (index >= 0) {
          slots[index] = true;
          resolve(index);
        } else {
          setTimeout(tryAcquire, 10);
        }
      };
      tryAcquire();
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private isRetryableError(error: any): boolean {
    const retryableCodes = [
      'messaging/internal-error',
      'messaging/server-unavailable',
      'messaging/timeout',
      'messaging/throttled',
    ];
    return retryableCodes.includes(error?.code);
  }

  private handleFailedBatch(batch: string[], results: FCMResult, error: any) {
    results.invalidTokens.push(...batch);
    results.totalFailed += batch.length;
    this.logger.error(`Batch failed: ${error.message}`);
  }

  private logResults(results: FCMResult) {
    this.logger.log(
      `Notification complete: ${results.totalSent} sent, ${results.totalFailed} failed`,
    );
  }

  private logBatchResult(
    batchNum: number,
    totalBatches: number,
    response: admin.messaging.BatchResponse,
    batchSize: number,
  ) {
    this.logger.log(
      `Batch ${batchNum}/${totalBatches}: ${response.successCount}/${batchSize} sent`,
    );
  }

  private logTokenError(token: string, error?: admin.FirebaseError) {
    this.logger.debug(
      `Token ${token} failed: ${error?.code} - ${error?.message}`,
    );
  }
}
