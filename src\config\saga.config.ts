import { registerAs } from '@nestjs/config';

interface SagaConfig {
  message: {
    enabled: boolean;
    timeout: number;
    maxRetries: number;
    compensationTimeout: number;
  };
  notification: {
    enabled: boolean;
    timeout: number;
    maxRetries: number;
    compensationTimeout: number;
  };
  media: {
    enabled: boolean;
    timeout: number;
    maxRetries: number;
    compensationTimeout: number;
  };
  global: {
    enabled: boolean;
    defaultTimeout: number;
    defaultMaxRetries: number;
    defaultCompensationTimeout: number;
    persistenceEnabled: boolean;
  };
}

export const sagaConfig = registerAs(
  'saga',
  (): SagaConfig => ({
    message: {
      enabled: process.env.SAGA_MESSAGE_ENABLED === 'true',
      timeout: parseInt(process.env.SAGA_MESSAGE_TIMEOUT || '300000', 10), // 5 minutes
      maxRetries: parseInt(process.env.SAGA_MESSAGE_MAX_RETRIES || '3', 10),
      compensationTimeout: parseInt(
        process.env.SAGA_MESSAGE_COMPENSATION_TIMEOUT || '60000',
        10,
      ), // 1 minute
    },
    notification: {
      enabled: process.env.SAGA_NOTIFICATION_ENABLED === 'true',
      timeout: parseInt(process.env.SAGA_NOTIFICATION_TIMEOUT || '120000', 10), // 2 minutes
      maxRetries: parseInt(
        process.env.SAGA_NOTIFICATION_MAX_RETRIES || '5',
        10,
      ),
      compensationTimeout: parseInt(
        process.env.SAGA_NOTIFICATION_COMPENSATION_TIMEOUT || '30000',
        10,
      ), // 30 seconds
    },
    media: {
      enabled: process.env.SAGA_MEDIA_ENABLED === 'true',
      timeout: parseInt(process.env.SAGA_MEDIA_TIMEOUT || '600000', 10), // 10 minutes
      maxRetries: parseInt(process.env.SAGA_MEDIA_MAX_RETRIES || '2', 10),
      compensationTimeout: parseInt(
        process.env.SAGA_MEDIA_COMPENSATION_TIMEOUT || '120000',
        10,
      ), // 2 minutes
    },
    global: {
      enabled: process.env.SAGA_ENABLED === 'true',
      defaultTimeout: parseInt(
        process.env.SAGA_DEFAULT_TIMEOUT || '300000',
        10,
      ), // 5 minutes
      defaultMaxRetries: parseInt(
        process.env.SAGA_DEFAULT_MAX_RETRIES || '3',
        10,
      ),
      defaultCompensationTimeout: parseInt(
        process.env.SAGA_DEFAULT_COMPENSATION_TIMEOUT || '60000',
        10,
      ), // 1 minute
      persistenceEnabled: process.env.SAGA_PERSISTENCE_ENABLED !== 'false', // Default true
    },
  }),
);
