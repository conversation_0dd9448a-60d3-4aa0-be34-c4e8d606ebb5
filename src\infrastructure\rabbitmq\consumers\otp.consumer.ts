import { Injectable, Logger } from '@nestjs/common';
import { RabbitSubscribe, AmqpConnection } from '@golevelup/nestjs-rabbitmq';
import {
  SmsService,
  SmsShouldGoToDlqError,
} from '../../../core/sms/sms.service';
import {
  OTP_DLQ_EXCHANGE,
  OTP_EXCHANGE,
  OTP_FAILED_ROUTING_KEY,
  OTP_QUEUE,
  OTP_SEND_ROUTING_KEY,
} from '../../../common/constants';
import { CircuitBreakerService } from '../../circuit-breaker/circuit-breaker.service';
import { EventMetricsService } from '../../monitoring/services/event-metrics.service';

@Injectable()
export class OtpConsumer {
  private readonly logger = new Logger(OtpConsumer.name);

  constructor(
    private readonly smsService: SmsService,
    private readonly amqpConnection: AmqpConnection,
    private readonly circuitBreakerService: CircuitBreakerService,
    private readonly metricsService: EventMetricsService,
  ) {}

  @RabbitSubscribe({
    exchange: OTP_EXCHANGE,
    routingKey: OTP_SEND_ROUTING_KEY,
    queue: OTP_QUEUE,
    queueOptions: {
      durable: true,
      arguments: {
        'x-dead-letter-exchange': OTP_DLQ_EXCHANGE,
        'x-dead-letter-routing-key': OTP_FAILED_ROUTING_KEY,
        'x-message-ttl': 15 * 60 * 1000,
        'x-max-retries': 3,
      },
    },
  })
  async handleOtpSend(message: {
    phoneNumber: string;
    otp: string;
    createdAt: string;
  }) {
    this.metricsService.incrementOtpRequestCount();
    try {
      // Check if message is expired
      const sentTime = message.createdAt
        ? new Date(message.createdAt).getTime()
        : Date.now();
      const now = Date.now();

      if (now - sentTime > 15 * 60 * 1000) {
        this.logger.warn(`Skipping expired OTP for ${message.phoneNumber}`);
        return; // Acknowledge message to remove from queue
      }

      this.logger.log(`Attempting to send OTP to ${message.phoneNumber}`);

      await this.circuitBreakerService.executeWithBreaker('smsService', () =>
        this.smsService.sendSmsVerification(message.phoneNumber, message.otp),
      );

      this.logger.log(`OTP sent successfully to ${message.phoneNumber}`);
      this.metricsService.incrementOtpSendSuccess();
    } catch (error) {
      this.logger.error(
        `Failed to send OTP to ${message.phoneNumber}:`,
        error.stack || error,
      );
      this.metricsService.incrementOtpSendFailure();

      // Check if it's a SmsShouldGoToDlqError - route directly to DLQ
      if (error instanceof SmsShouldGoToDlqError) {
        this.logger.warn(
          `Routing message to DLQ for ${message.phoneNumber}: ${error.message}`,
        );

        await this.routeMessageToDlq(message, error.message);
        return; // Acknowledge the original message
      }

      // Check if it's a permanent error (legacy support)
      if (this.isPermanentError(error)) {
        this.logger.warn(
          `Permanent error for ${message.phoneNumber}. Message will be acknowledged.`,
        );
        return; // Don't throw - acknowledge the message
      }

      // For temporary errors, throw to trigger retry
      throw error;
    }
  }

  private async routeMessageToDlq(
    message: { phoneNumber: string; otp: string; createdAt: string },
    reason: string,
  ) {
    try {
      // Publish the message to the DLQ with additional context
      await this.amqpConnection.publish(
        OTP_DLQ_EXCHANGE,
        OTP_FAILED_ROUTING_KEY,
        {
          ...message,
          failureReason: reason,
          failedAt: new Date().toISOString(),
          originalQueue: OTP_QUEUE,
        },
      );

      this.logger.log(
        `Successfully routed message to DLQ for ${message.phoneNumber}`,
      );
    } catch (dlqError) {
      this.logger.error(
        `Failed to route message to DLQ for ${message.phoneNumber}:`,
        dlqError,
      );
    }
  }

  private isPermanentError(error: any): boolean {
    // SMS limit exceeded
    if (error?.response?.data?.message) {
      const message = error.response.data.message.toLowerCase();
      if (message.includes('exceeded') && message.includes('limit')) {
        return true;
      }
    }

    // HTTP status codes that indicate permanent errors
    const status = error?.response?.status;
    if ([400, 401, 403, 404, 422].includes(status)) {
      return true;
    }

    return false;
  }
}
