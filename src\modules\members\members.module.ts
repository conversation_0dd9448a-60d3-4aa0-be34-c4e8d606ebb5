import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MemberFcmToken } from './entities/member-fcm-token.entity';
import { MembersService } from './services/members.service';
import { MembersController } from './controllers/members.controller';
import { SocketModule } from '../../infrastructure/socket/socket.module';
import { OrgMember } from './entities/org-member.entity';
import { UserModule } from '../users/users.module';
import { OrganizationsModule } from '../organization/organizations.module';
import { CoreModule } from '../../core/core.module';
import { InfrastructureModule } from '../../infrastructure/infrastructure.module';
import { SecurityModule } from '../security/security.module';
import { AuditModule } from '../audit/audit.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([OrgMember, MemberFcmToken]),
    CoreModule,
    UserModule,
    OrganizationsModule,
    SocketModule,
    InfrastructureModule,
    SecurityModule,
    AuditModule,
  ],
  providers: [MembersService],
  controllers: [MembersController],
  exports: [MembersService, TypeOrmModule],
})
export class MembersModule {}
