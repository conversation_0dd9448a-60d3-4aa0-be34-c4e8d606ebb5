import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EventStoreService } from './event-store.service';
import { StoredEvent } from './entities/stored-event.entity';
import { EventSnapshot } from './entities/event-snapshot.entity';

@Module({
  imports: [TypeOrmModule.forFeature([StoredEvent, EventSnapshot])],
  providers: [EventStoreService],
  exports: [EventStoreService],
})
export class EventStoreModule {}
