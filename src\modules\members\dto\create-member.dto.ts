import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEmail, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateMemberDto {
  @ApiProperty({
    example: '<PERSON>',
    description: 'Name of the new member',
  })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({
    example: '+1987654321',
    description: 'Phone number of the new member',
  })
  @IsNotEmpty()
  @IsString()
  phoneNo: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email of the new member',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiPropertyOptional({
    example: 'https://cdn.example.com/avatar.png',
    description: 'Optional profile picture URL',
  })
  @IsOptional()
  @IsString()
  fileUrl?: string;
}
