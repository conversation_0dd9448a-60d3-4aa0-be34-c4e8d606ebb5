import { Injectable, Inject, OnModule<PERSON><PERSON><PERSON>, Lo<PERSON> } from '@nestjs/common';
import {
  LOGIN_CHALLENGE,
  REDIS_CLIENT,
  REDIS_KEY_PREFIX,
  REFRESH_CHALLENGE,
  REFRESH_TOKEN_KEY,
  REFRESH_TOKEN_TTL,
} from '../constants/redis.constants';
import { RedisClient } from '../interfaces/redis-client.interface';
import { UserType } from '../../../common/types/user-type';
import {
  RefreshTokenData,
  ThrottlerIncrementResult,
} from '../interfaces/redis.types';
import {
  THROTTLER_BLOCK_KEY_PREFIX,
  THROTTLER_REDIS_KEY_PREFIX,
} from 'src/common/constants/throttler.constants';

@Injectable()
export class RedisService implements OnModuleDestroy {
  private readonly logger = new Logger(RedisService.name);

  constructor(
    @Inject(REDIS_CLIENT) private readonly redisClient: RedisClient,
  ) {}

  /**
   * Generate consistent Redis key for refresh tokens
   */
  private getRefreshTokenKey(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): string {
    return `${REDIS_KEY_PREFIX}:${REFRESH_TOKEN_KEY}:${userType}:${userId}:${tokenId}`;
  }

  /**
   * Generate meta key for refresh token metadata
   */
  private getMetaKey(key: string): string {
    return `${key}:meta`;
  }

  /**
   * Generate pattern for user's refresh tokens
   */
  private getUserTokenPattern(userType: UserType, userId: number): string {
    return `${REDIS_KEY_PREFIX}:${REFRESH_TOKEN_KEY}:${userType}:${userId}:*`;
  }

  /**
   * Store refresh token with metadata
   */
  async storeRefreshToken(
    userType: UserType,
    userId: number,
    refreshToken: string,
    tokenId: string,
    userAgent?: string,
    deviceName?: string,
    ip?: string,
    expiresIn: number = REFRESH_TOKEN_TTL,
  ): Promise<void> {
    const key = this.getRefreshTokenKey(userType, userId, tokenId);
    const metaKey = this.getMetaKey(key);

    try {
      const pipeline = this.redisClient.pipeline();

      pipeline.set(key, refreshToken, 'EX', expiresIn);

      const metaData: Record<string, string> = {
        createdAt: Date.now().toString(),
        ...(userAgent ? { userAgent } : {}),
        ...(deviceName ? { deviceName } : {}),
        ...(ip ? { ip } : {}),
      };

      pipeline.hmset(metaKey, metaData);
      pipeline.expire(metaKey, expiresIn);

      await pipeline.exec();

      this.logger.debug(
        `Stored refresh token for user ${userId} with tokenId ${tokenId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to store refresh token for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Retrieve refresh token by userType, userId and tokenId
   */
  async getRefreshToken(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): Promise<string | null> {
    try {
      const key = this.getRefreshTokenKey(userType, userId, tokenId);
      return await this.redisClient.get(key);
    } catch (error) {
      this.logger.error(
        `Failed to get refresh token for user ${userId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Get refresh token with metadata
   */
  async getRefreshTokenWithMeta(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): Promise<RefreshTokenData | null> {
    try {
      const key = this.getRefreshTokenKey(userType, userId, tokenId);
      const metaKey = this.getMetaKey(key);

      const [token, meta] = await Promise.all([
        this.redisClient.get(key),
        this.redisClient.hgetall(metaKey),
      ]);

      if (!token) {
        return null;
      }

      return {
        token,
        deviceFingerprint: meta?.deviceFingerprint,
        userAgent: meta?.userAgent || '',
        deviceName: meta?.deviceName || '',
        createdAt: meta?.createdAt || '',
        ip: meta?.ip,
      };
    } catch (error) {
      this.logger.error(
        `Failed to get refresh token with meta for user ${userId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Remove a specific refresh token
   */
  async removeRefreshToken(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): Promise<void> {
    try {
      const key = this.getRefreshTokenKey(userType, userId, tokenId);
      const metaKey = this.getMetaKey(key);

      const deletedCount = await this.redisClient.del(key, metaKey);

      if (deletedCount > 0) {
        this.logger.debug(
          `Removed refresh token for user ${userId} with tokenId ${tokenId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to remove refresh token for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Remove all refresh tokens for a user
   */
  async removeAllRefreshTokensForUser(
    userType: UserType,
    userId: number,
  ): Promise<void> {
    try {
      const pattern = this.getUserTokenPattern(userType, userId);
      const keys = await this.redisClient.keys(pattern);

      if (keys.length > 0) {
        // Get all meta keys as well
        const metaKeys = keys.map((key) => this.getMetaKey(key));
        const allKeys = [...keys, ...metaKeys];

        // Use pipeline for batch deletion
        const pipeline = this.redisClient.pipeline();
        allKeys.forEach((key) => pipeline.del(key));
        await pipeline.exec();

        this.logger.debug(
          `Removed ${keys.length} refresh tokens for user ${userId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Failed to remove all refresh tokens for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  private getUserPublicKeyKey(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): string {
    return `${REDIS_KEY_PREFIX}:publickey:${userType}:${userId}:${tokenId}`;
  }

  /**
   * Store public key for a specific refresh token
   */
  async storePublicKey(
    userType: UserType,
    userId: number,
    tokenId: string,
    publicKey: string,
    expiresIn: number = REFRESH_TOKEN_TTL,
  ): Promise<void> {
    try {
      const key = this.getUserPublicKeyKey(userType, userId, tokenId);
      await this.redisClient.set(key, publicKey, 'EX', expiresIn);

      this.logger.debug(
        `Stored public key for user ${userId} with tokenId ${tokenId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to store public key for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Retrieve public key by userType, userId and tokenId
   */
  async getPublicKey(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): Promise<string | null> {
    try {
      const key = this.getUserPublicKeyKey(userType, userId, tokenId);
      return await this.redisClient.get(key);
    } catch (error) {
      this.logger.error(`Failed to get public key for user ${userId}:`, error);
      return null;
    }
  }

  /**
   * Remove public key for a specific token
   */
  async removePublicKey(
    userType: UserType,
    userId: number,
    tokenId: string,
  ): Promise<void> {
    try {
      const key = this.getUserPublicKeyKey(userType, userId, tokenId);
      await this.redisClient.del(key);

      this.logger.debug(
        `Removed public key for user ${userId} with tokenId ${tokenId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to remove public key for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  async migratePublicKey(
    userType: UserType,
    userId: number,
    oldTokenId: string,
    newTokenId: string,
    publicKey: string,
    expiresIn: number = REFRESH_TOKEN_TTL,
  ): Promise<void> {
    try {
      const oldKey = this.getUserPublicKeyKey(userType, userId, oldTokenId);
      const newKey = this.getUserPublicKeyKey(userType, userId, newTokenId);

      // Use a pipeline for atomic operation
      const pipeline = this.redisClient.pipeline();

      // Store with new token ID
      pipeline.set(newKey, publicKey, 'EX', expiresIn);
      // Delete the old key
      pipeline.del(oldKey);

      await pipeline.exec();

      this.logger.debug(
        `Migrated public key for user ${userId} from ${oldTokenId} to ${newTokenId}`,
      );
    } catch (error) {
      this.logger.error(
        `Failed to migrate public key for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Generate consistent Redis key for refresh challenges
   */
  private getChallengeKey(challenge: string): string {
    return `${REDIS_KEY_PREFIX}:${REFRESH_CHALLENGE}:${challenge}`;
  }

  private getLoginChallengeKey(challenge: string): string {
    return `${REDIS_KEY_PREFIX}:${LOGIN_CHALLENGE}:${challenge}`;
  }

  async storeLoginChallenge(
    challenge: string,
    username: string,
    publicKey: string,
    expiresIn: number = 300,
  ): Promise<void> {
    const key = this.getLoginChallengeKey(challenge);
    const challengeData = {
      username,
      publicKey,
      createdAt: Date.now().toString(),
    };

    await this.redisClient.set(
      key,
      JSON.stringify(challengeData),
      'EX',
      expiresIn,
    );
  }

  async getAndValidateLoginChallenge(
    challenge: string,
  ): Promise<{ username: string; publicKey: string } | null> {
    const key = this.getLoginChallengeKey(challenge);
    const challengeData = await this.redisClient.get(key);

    if (!challengeData) return null;

    const { username, publicKey } = JSON.parse(challengeData);

    await this.redisClient.del(key);
    return { username, publicKey };
  }

  /**
   * Store a refresh challenge with user context
   */
  async storeRefreshChallenge(
    challenge: string,
    userType: UserType,
    userId: number,
    tokenId: string,
    expiresIn: number = 60,
  ): Promise<void> {
    try {
      const key = this.getChallengeKey(challenge);
      const challengeData = {
        userType,
        userId,
        tokenId,
        createdAt: Date.now().toString(),
      };

      await this.redisClient.set(
        key,
        JSON.stringify(challengeData),
        'EX',
        expiresIn,
      );

      this.logger.debug(`Stored refresh challenge for user ${userId}`);
    } catch (error) {
      this.logger.error(
        `Failed to store refresh challenge for user ${userId}:`,
        error,
      );
      throw error;
    }
  }

  /**
   * Retrieve and validate a refresh challenge
   */
  async getAndValidateChallenge(challenge: string): Promise<{
    userType: UserType;
    userId: number;
    tokenId: string;
  } | null> {
    try {
      const key = this.getChallengeKey(challenge);
      const challengeData = await this.redisClient.get(key);

      if (!challengeData) {
        return null; // Challenge not found or expired
      }

      // Delete the challenge immediately after retrieval (one-time use)
      await this.redisClient.del(key);

      return JSON.parse(challengeData);
    } catch (error) {
      this.logger.error('Failed to get refresh challenge:', error);
      return null;
    }
  }

  async throttlerIncrement(
    key: string,
    ttl: number,
    limit: number,
    blockDuration: number,
    throttlerName: string,
  ): Promise<ThrottlerIncrementResult> {
    const throttlerKey = `${THROTTLER_REDIS_KEY_PREFIX}:${throttlerName}:${key}`;
    const blockKey = `${THROTTLER_BLOCK_KEY_PREFIX}:${throttlerName}:${key}`;

    try {
      const luaScript = `
      -- 1. Check if blocked
      local blockedTtl = redis.call('TTL', KEYS[2])
      if blockedTtl > 0 then
        return { -1, blockedTtl, 1 }  -- special flag: blocked
      end

      -- 2. Increment request counter
      local current = redis.call('INCR', KEYS[1])
      if current == 1 then
        redis.call('EXPIRE', KEYS[1], ARGV[1])
      end
      local ttl = redis.call('TTL', KEYS[1])

      -- 3. If over limit → block
      if current > tonumber(ARGV[2]) then
        redis.call('SETEX', KEYS[2], ARGV[3], 1)
        return { current, ARGV[3], 1 } -- blocked, return block duration
      end

      -- 4. Normal response
      return { current, ttl, 0 }
    `;

      const result = (await this.redisClient.eval(
        luaScript,
        2, // two keys
        throttlerKey,
        blockKey,
        ttl, // ARGV[1] = TTL
        limit, // ARGV[2] = limit
        blockDuration, // ARGV[3] = block duration
      )) as [number, number, number];

      const [hitsOrFlag, expire, blocked] = result;

      if (blocked === 1) {
        return {
          totalHits: hitsOrFlag === -1 ? limit : hitsOrFlag,
          timeToExpire: expire,
          isBlocked: true,
          timeToBlockExpire: expire,
        };
      }

      return {
        totalHits: hitsOrFlag,
        timeToExpire: expire,
        isBlocked: false,
        timeToBlockExpire: 0,
      };
    } catch (error) {
      this.logger.error('Throttler Redis error:', error);
      // Fallback: allow if Redis fails
      return {
        totalHits: 1,
        timeToExpire: ttl,
        isBlocked: false,
        timeToBlockExpire: 0,
      };
    }
  }

  /**
   * Health check method to verify Redis connection
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.redisClient.ping();
      return result === 'PONG';
    } catch (error) {
      this.logger.error('Redis health check failed:', error);
      return false;
    }
  }

  /**
   * Get count of active refresh tokens for a user
   */
  async getUserTokenCount(userType: UserType, userId: number): Promise<number> {
    try {
      const pattern = this.getUserTokenPattern(userType, userId);
      const keys = await this.redisClient.keys(pattern);
      return keys.filter((key) => !key.endsWith(':meta')).length;
    } catch (error) {
      this.logger.error(`Failed to get token count for user ${userId}:`, error);
      return 0;
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      this.logger.warn('Closing Redis connection...');
      await this.redisClient.quit();
    } catch (error) {
      this.logger.error('Error closing Redis connection:', error);
    }
  }
}
