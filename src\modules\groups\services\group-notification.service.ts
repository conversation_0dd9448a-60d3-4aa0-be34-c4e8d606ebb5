import { Injectable, Logger } from '@nestjs/common';
import { Group } from '../entities/group.entity';
import { StorageService } from 'src/core/storage/storage.service';
import { NotificationService } from 'src/infrastructure/notification/services/notification.service';
import { GroupEncryptionKey } from '../entities/group-encryption-keys.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { EVENT_NAMES } from 'src/common/constants/event-names';

@Injectable()
export class GroupNotificationService {
  private readonly logger = new Logger(GroupNotificationService.name);
  constructor(
    private readonly notificationService: NotificationService,
    private readonly storageService: StorageService,
    @InjectRepository(GroupEncryptionKey)
    private groupEncryptionKeyRepository: Repository<GroupEncryptionKey>,
  ) {}

  async emitGroupInfoToNewMembers(
    group: Group,
    memberIds: number[],
    verifiedMembers: any[],
    latestSeq: number,
  ) {
    const signedUrl = await this.storageService.generateSignedUrl(
      group.imageUrl,
      315360000,
    );

    const membersToEmit = verifiedMembers.filter((m) =>
      memberIds.includes(m.id),
    );

    for (const memberId of memberIds) {
      const encryptedKey = await this.groupEncryptionKeyRepository.findOne({
        where: {
          groupId: group.id,
          memberId,
          keyVersion: group.currentKeyVersion,
          isActive: true,
        },
      });

      const payload = {
        id: group.id,
        type: 'group',
        name: group.name,
        encryptedGroupKey: encryptedKey?.encryptedGroupKey || '',
        encryptedGroupKeyVersion: encryptedKey?.keyVersion || '',
        date: group.createdAt,
        imageUrl: signedUrl,
        msg: '',
        msgNonce: '',
        msgGroupKeyVersion: '',
        read: true,
        unreadCount: 0,
        members: membersToEmit,
        lastSender: null,
        lastSenderID: null,
        createdAt: group.createdAt.toISOString(),
        latestSeq,
      };

      await this.notificationService.broadcastToMemberWithAck(
        memberId,
        EVENT_NAMES.GROUP_INFO,
        payload,
      );
    }
  }

  async emitGroupUpdatesToExistingMembers(
    group: Group,
    memberIds: number[],
    changes: {
      newMembers: any[];
      leftMembers: any[];
      newKeyVersion: number | null;
    },
    latestSeq: number,
  ) {
    const signedUrl = await this.storageService.generateSignedUrl(
      group.imageUrl,
      315360000,
    );

    const basePayload = {
      groupId: group.id,
      type: 'group',
      name: group.name,
      imageUrl: signedUrl,
      timestamp: new Date(),
      latestSeq,
      changes: {
        newMembers: changes.newMembers,
        leftMembers: changes.leftMembers,
      },
    };

    for (const memberId of memberIds) {
      let encryptedKeyPayload = {};

      if (changes.newKeyVersion !== null) {
        const encryptedKey = await this.groupEncryptionKeyRepository.findOne({
          where: {
            groupId: group.id,
            memberId,
            keyVersion: changes.newKeyVersion,
            isActive: true,
          },
        });

        if (encryptedKey) {
          encryptedKeyPayload = {
            encryptedGroupKey: encryptedKey.encryptedGroupKey,
            keyVersion: changes.newKeyVersion,
          };
        }
      }

      const memberSpecificPayload = {
        ...basePayload,
        ...encryptedKeyPayload,
      };

      await this.notificationService.broadcastToMemberWithAck(
        memberId,
        EVENT_NAMES.GROUP_UPDATE,
        memberSpecificPayload,
      );
    }
  }

  async emitGroupMetaUpdates(
    group: Group,
    memberIds: number[],
    options: {
      changes: {
        name?: { old: string; new: string };
        imageUrl?: { old: string; new: string };
        description?: { old: string; new: string };
        isActive?: { old: boolean; new: boolean };
      };
      latestSeq: number;
      signedImageUrl?: string | null;
    },
  ) {
    // Prepare base payload
    const payload = {
      groupId: group.id,
      type: 'group',
      timestamp: new Date(),
      latestSeq: options.latestSeq,
      changes: options.changes,
      // Include current values
      current: {
        name: group.name,
        description: group.description,
        imageUrl: options.signedImageUrl || group.imageUrl,
        isActive: !group.deletedAt,
      },
    };

    // Emit to each verified member
    for (const memberId of memberIds) {
      try {
        await this.notificationService.broadcastToMemberWithAck(
          memberId,
          EVENT_NAMES.GROUP_META_UPDATE,
          payload,
        );
      } catch (error) {
        this.logger.error(
          `Failed to emit meta update to member ${memberId}`,
          error,
        );
      }
    }
  }
}
