import { Module } from '@nestjs/common';
import { NotificationService } from './services/notification.service';
import { SocketModule } from '../socket/socket.module';
import { NotificationController } from './notification.controller';
import { RedisModule } from '../redis/redis.module';
import { FcmService } from './services/fcm.service';

@Module({
  imports: [SocketModule, RedisModule],
  controllers: [NotificationController],
  providers: [NotificationService, FcmService],
  exports: [NotificationService, FcmService],
})
export class NotificationModule {}
