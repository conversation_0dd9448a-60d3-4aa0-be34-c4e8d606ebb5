import {
  IsEmail,
  IsNotEmpty,
  <PERSON><PERSON><PERSON>ber,
  <PERSON><PERSON>ptional,
  IsString,
} from 'class-validator';
import { CreateOrganizationDto } from '../../organization/dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateUserDto {
  @ApiPropertyOptional({
    description: 'Username of the user',
    example: 'jane_doe',
  })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional({
    description: 'Password for the account',
    minLength: 6,
    example: 'strongPassword123',
  })
  @IsOptional()
  @IsString()
  password?: string;

  @ApiProperty({
    description: 'Email of the user',
    example: '<EMAIL>',
  })
  @IsNotEmpty()
  @IsEmail()
  email: string;

  @ApiPropertyOptional({ description: 'Role ID (defaults to 2)', example: 2 })
  @IsOptional()
  @IsNumber()
  roleId: number = 2;

  @ApiPropertyOptional({
    description: 'Profile picture URL',
    example: 'https://example.com/avatar.png',
  })
  @IsOptional()
  @IsString()
  fileUrl?: string;

  @ApiPropertyOptional({
    description: 'Organization details (optional)',
    type: () => CreateOrganizationDto,
  })
  @IsOptional()
  organization?: CreateOrganizationDto;
}
