import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Param,
  Post,
  Put,
  Req,
  Res,
  UnauthorizedException,
  UseInterceptors,
} from '@nestjs/common';
import { ImageUrlInterceptor } from '../../../../common/interceptors/image-url.interceptor';
import { GroupUserService } from '../services/group-user.service';
import { AllocateMemberDto } from 'src/modules/groups/dto/allocate-member.dto';
import { GroupMember } from '@nestjs/microservices/external/kafka.interface';
import { Response, Request } from 'express';
import { OrgAdminOnly } from 'src/common/decorators/roles.decorator';

@Controller('group-users')
export class UserGroupsController {
  constructor(private readonly groupUserService: GroupUserService) {}

  @Get()
  @HttpCode(HttpStatus.OK)
  @UseInterceptors(ImageUrlInterceptor)
  async getUserGroups(@Req() req: Request) {
    const memberId = req.user.id;
    const groups = await this.groupUserService.getUserGroups(memberId);

    return groups;
  }

  @Post('allocate-members/:userId')
  @HttpCode(HttpStatus.OK)
  @OrgAdminOnly()
  async allocateMembersToGroup(
    @Param('userId') userId: string,
    @Body() allocateMemberDto: AllocateMemberDto,
    @Res({ passthrough: true }) response: Response,
  ): Promise<GroupMember[]> {
    const allocatedMembers = await this.groupUserService.allocateGroupMembers(
      allocateMemberDto,
      userId,
    );

    response.locals.message = 'Members allocated to group successfully';

    return allocatedMembers;
  }

  @Put(':userId/update-members')
  async updateGroupMembers(
    @Param('userId') userId: string,
    @Req() req: Request,
    @Body() updateGroupMemberDto: AllocateMemberDto,
  ) {
    (req as any).res.locals.message = 'Group members updated successfully';

    const user = req.user;

    if (Number(userId) !== Number(user.id)) {
      throw new UnauthorizedException();
    }

    return await this.groupUserService.allocateGroupMembers(
      updateGroupMemberDto,
      userId,
    );
  }
}
