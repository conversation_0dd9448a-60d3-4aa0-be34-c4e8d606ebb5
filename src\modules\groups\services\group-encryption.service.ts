import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { Group } from '../entities/group.entity';
import { GroupEncryptionKey } from '../entities/group-encryption-keys.entity';
import { OrgMember } from '../../members/entities/org-member.entity';
import { MessageSecurityService } from '../../../core/encryption/message-security.service';

@Injectable()
export class GroupEncryptionService {
  constructor(
    private readonly dataSource: DataSource,
    private readonly messageSecurityService: MessageSecurityService,
    @InjectRepository(GroupEncryptionKey)
    private readonly groupEncryptionKeyRepository: Repository<GroupEncryptionKey>,
  ) {}

  /**
   * Create initial encryption keys for a new group
   * @param groupId The ID of the newly created group
   * @param adminId The admin's org member ID
   * @param initialMemberIds Array of member IDs to add to the group (can be empty for groups created without members)
   */
  async setupGroupEncryption(
    groupId: number,
    creatorSecretKey: string,
    initialMemberIds: number[] = [],
  ): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Get all org members that will be part of this group
      const members = await queryRunner.manager.find(OrgMember, {
        where: {
          id: In(initialMemberIds),
        },
      });

      if (members.length !== initialMemberIds.length) {
        throw new Error('Some members not found');
      }

      // Extract public keys from members
      const memberPublicKeys = members.map((member) => {
        if (!member.identityKey?.publicKey) {
          throw new Error(`Public key not found for member ${member.id}`);
        }
        return member.identityKey?.publicKey;
      });

      // Create group conversation
      const groupConversation =
        this.messageSecurityService.createGroupConversation(
          creatorSecretKey,
          memberPublicKeys,
        );

      // Update group with key version
      await queryRunner.manager.update(
        Group,
        { id: groupId },
        {
          currentKeyVersion: 1,
        },
      );

      // Store encrypted group keys
      const groupEncryptionKeys = groupConversation.encryptedGroupKeys.map(
        (encryptedKey, index) => {
          return this.groupEncryptionKeyRepository.create({
            groupId,
            memberId: initialMemberIds[index],
            encryptedGroupKey: encryptedKey.encryptedGroupKey,
            keyVersion: 1,
            isActive: true,
          });
        },
      );

      await queryRunner.manager.save(groupEncryptionKeys);
      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Allocate new members to a group and rotate the group encryption key
   * @param groupId The group to add members to
   * @param adminSecretKey The admin's secret key for encrypting the group key
   * @param newMemberIds Array of new member IDs to add to the group
   * @param existingMemberIds Array of existing member IDs in the group
   */
  async allocateMembersWithKeyRotation(
    groupId: number,
    adminSecretKey: string,
    newMemberIds: number[],
    existingMemberIds: number[],
  ): Promise<number> {
    // Now returns the new key version
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const group = await queryRunner.manager.findOne(Group, {
        where: { id: groupId },
      });

      if (!group) throw new Error(`Group with ID ${groupId} not found`);

      const allMemberIds = [...existingMemberIds, ...newMemberIds];
      const allMembers = await queryRunner.manager.find(OrgMember, {
        where: { id: In(allMemberIds) },
        relations: ['identityKey'],
      });

      if (allMembers.length !== allMemberIds.length) {
        throw new Error('Some members not found');
      }

      const newKeyVersion = group.currentKeyVersion + 1;
      const { encryptedGroupKeys } = this.messageSecurityService.rotateGroupKey(
        adminSecretKey,
        allMembers.map((m) => m.identityKey?.publicKey),
      );

      await queryRunner.manager.update(
        Group,
        { id: groupId },
        { currentKeyVersion: newKeyVersion },
      );

      await queryRunner.manager.update(
        GroupEncryptionKey,
        { groupId, isActive: true },
        { isActive: false },
      );

      const groupEncryptionKeys = encryptedGroupKeys.map(
        (encryptedKey, index) => ({
          groupId,
          memberId: allMemberIds[index],
          encryptedGroupKey: encryptedKey.encryptedGroupKey,
          keyVersion: newKeyVersion,
          isActive: true,
        }),
      );

      await queryRunner.manager.save(GroupEncryptionKey, groupEncryptionKeys);
      await queryRunner.commitTransaction();

      return newKeyVersion; // Return the new key version
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  /**
   * Get active group key for a specific member
   * @param groupId The group ID
   * @param memberId The member ID
   * @returns The encrypted group key that the member can decrypt
   */
  async getActiveGroupKeyForMember(
    groupId: number,
    memberId: number,
  ): Promise<GroupEncryptionKey | null> {
    return this.groupEncryptionKeyRepository.findOne({
      where: {
        groupId,
        memberId,
        isActive: true,
      },
    });
  }

  async getUserGroupKey(
    groupId: number,
    memberId: number,
    keyVersion: number,
  ): Promise<string | null> {
    const encryptionKeyEntry = await this.groupEncryptionKeyRepository.findOne({
      where: {
        groupId,
        memberId,
        keyVersion,
        isActive: true,
      },
    });

    return encryptionKeyEntry?.encryptedGroupKey || null;
  }
}
