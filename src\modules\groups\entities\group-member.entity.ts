import {
  <PERSON><PERSON>ty,
  PrimaryGeneratedColumn,
  Column,
  ManyToOne,
  JoinColumn,
  UpdateDateColumn,
} from 'typeorm';

@Entity('group_members')
export class GroupMember {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'group_id' })
  groupId: number;

  @Column({ name: 'member_id' })
  memberId: number;

  @Column({ name: 'is_active', default: true })
  isActive: boolean;

  @Column({ name: 'joined_at' })
  joinedAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'left_at', nullable: true })
  leftAt: Date;

  @Column({ name: 'is_mute', default: false })
  isMute: boolean;

  @ManyToOne('Group', 'members')
  @JoinColumn({ name: 'group_id' })
  group: any;

  @ManyToOne('OrgMember', 'groupMemberships')
  @JoinColumn({ name: 'member_id' })
  member: any;
}
