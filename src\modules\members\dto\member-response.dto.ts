import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  EncryptedPrivateKey,
  IdentityKeyEntity,
} from '../../security/entities/identity-key.entity';
import { OrgMember } from '../entities/org-member.entity';

export class IdentityKeyDto {
  @ApiProperty({
    example: 'MIIBIjANBgkqh...',
    description: 'Public key of the member',
  })
  publicKey: string;

  @ApiPropertyOptional({ description: 'Encrypted private key if available' })
  encryptedPrivateKey?: EncryptedPrivateKey;

  constructor(identityKey: IdentityKeyEntity) {
    this.publicKey = identityKey.publicKey;
    this.encryptedPrivateKey = identityKey.encryptedPrivateKey;
  }
}
export class MemberResponseDto {
  @ApiProperty({ example: 1, description: 'Unique ID of the member' })
  id: number;

  @ApiProperty({ example: '<PERSON>', description: 'Full name of the member' })
  name: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'Email address of the member',
  })
  email: string;

  @ApiProperty({
    example: '+1234567890',
    description: 'Phone number of the member',
  })
  phoneNo: string;

  @ApiPropertyOptional({
    example: 'https://cdn.example.com/profile.jpg',
    description: 'Profile image URL',
  })
  imageUrl?: string;

  @ApiProperty({ example: 101, description: 'Organization ID of the member' })
  orgId: number;

  @ApiPropertyOptional({
    type: IdentityKeyDto,
    description: 'Member’s identity key pair',
  })
  identityKey?: IdentityKeyDto;

  constructor(member: OrgMember) {
    this.id = member.id;
    this.name = member.name;
    this.email = member.email;
    this.phoneNo = member.phoneNo;
    this.imageUrl = member.imageUrl;
    this.orgId = member.orgId;
    this.identityKey = member.identityKey
      ? new IdentityKeyDto(member.identityKey)
      : undefined;
  }
}
