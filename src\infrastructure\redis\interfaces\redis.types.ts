// Presence status types
export type PresenceStatus = 'online' | 'offline' | 'away';

// Refresh token data interface
export interface RefreshTokenData {
  token: string;
  deviceFingerprint: string;
  userAgent?: string;
  deviceName?: string;
  createdAt: string;
  ip?: string;
}

// Session interface for active user sessions
export interface Session {
  tokenId: string;
  deviceFingerprint: string;
  userAgent?: string;
  deviceName?: string;
  createdAt: string;
  ip: string;
}

// Member status interface for presence tracking
export interface MemberStatus {
  memberId: number;
  status: PresenceStatus;
  devices: string[];
  activeDevices: number;
  lastSeen?: number;
}

// Device information interface
export interface DeviceInfo {
  devices: string[];
  activeDevices: string[];
}

// Presence update message structure for pub/sub
export interface PresenceUpdateMessage {
  memberId: number;
  status: PresenceStatus;
  lastSeen: number | null;
  timestamp: string;
  devices: number;
  activeDevices: number;
}

// Device presence information
export interface DevicePresence {
  deviceId: string;
  timestamp: string | null;
  active: boolean;
}

// Debug information for presence tracking
export interface PresenceDebugInfo {
  memberDevices: string[];
  devicePresences: DevicePresence[];
  overallStatus: MemberStatus;
  hashEntry: string | null;
}

// FCM token storage interface
export interface FcmTokenInfo {
  memberId: number;
  deviceId: string;
  token: string;
  createdAt: number;
}

// Session metadata for tracking
export interface SessionMetadata {
  createdAt: string;
  userAgent: string;
  deviceFingerprint?: string;
  ip?: string;
}

// Redis pipeline result type
export interface PipelineResult {
  error: Error | null;
  result: any;
}

// Configuration interfaces
export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db?: number;
}

export interface PresenceConfig {
  deviceTtl: number; // Device presence TTL in seconds
  memberDevicesTtl: number; // Member devices set TTL in seconds
  awayThreshold: number; // Threshold for away status in milliseconds
  cleanupInterval: string; // Cron expression for cleanup
}

export interface ThrottlerIncrementResult {
  totalHits: number;
  timeToExpire: number;
  isBlocked?: boolean;
  timeToBlockExpire?: number;
}
